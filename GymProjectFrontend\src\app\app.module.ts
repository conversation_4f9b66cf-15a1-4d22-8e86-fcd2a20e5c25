import { LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CompanyuserComponent } from './components/companyuser/companyuser.component';
import { SidebarComponent } from './components/navi/sidebar.component';
import { CompanySelectorComponent } from './components/company-selector/company-selector.component';
import { MemberComponent } from './components/member/member.component';
import { CompanyUserDetailsComponent } from './components/company-user-details/company-user-details.component';
import { CompanyUserDetailDialogComponent } from './components/company-user-detail-dialog/company-user-detail-dialog.component';
import { DeletedCompaniesComponent } from './components/deleted-companies/deleted-companies.component';
import { HelpDialogComponent } from './components/help-dialog/help-dialog.component';
import { HelpButtonComponent } from './components/help-button/help-button.component';
import { MembershiptypeComponent } from './components/membershiptype/membershiptype.component';
import { MemberentryexithistoryComponent } from './components/memberentryexithistory/memberentryexithistory.component';
import { MemberRemainingDayComponent } from './components/member-remaining-day/member-remaining-day.component';
import { CityComponent } from './components/city/city.component';
import { MemberDetailDialogComponent } from './components/member-detail-dialog/member-detail-dialog.component';
import { CompanyUnifiedAddComponent } from './components/crud/company-unified-add/company-unified-add.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FilterPipePipe } from './pipes/companyuser-filter-pipe.pipe';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { MemberFilterPipePipe } from './pipes/memberfilter-pipe.pipe';
import { MemberFilterComponent } from './components/member-filter/member-filter.component';
import { AllmemberfilterpipePipe } from './pipes/allmemberfilterpipe.pipe';
import { ActiveMembersPipe } from './pipes/active-members.pipe';
import { PaymentHistoryComponent } from './components/payment-history/payment-history.component';
import { ToastrModule } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MAT_DATE_LOCALE, MatOptionModule, MAT_DATE_FORMATS } from '@angular/material/core'; // MAT_DATE_FORMATS eklendi
import { MemberAddComponent } from './components/crud/member-add/member-add.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MembershiptypeAddComponent } from './components/crud/membershiptype-add/membershiptype-add.component';
import { MembershipAddComponent } from './components/crud/membership-add/membership-add.component';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withFetch,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { NgxPaginationModule } from 'ngx-pagination';
import { PaymenthistoryfilterPipePipe } from './pipes/paymenthistoryfilter-pipe.pipe';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MemberUpdateComponent } from './components/crud/member-update/member-update.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { registerLocaleData, CommonModule } from '@angular/common'; // CommonModule eklendi
import localeTr from '@angular/common/locales/tr';
import { MembershiptypeUpdateComponent } from './components/crud/membershiptype-update/membershiptype-update.component';
import { MembershipUpdateComponent } from './components/crud/membership-update/membership-update.component';
import { LoginComponent } from './components/login/login.component';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { AppUnauthorizedComponent } from './components/app-unauthorized/app-unauthorized.component';
import { MemberQRCodeComponent } from './components/member-qrcode/member-qrcode.component';
import { DebtorMemberComponent } from './components/debtor-member/debtor-member.component';
import { ConfirmationDialogComponent } from './components/confirmation-dialog/confirmation-dialog.component';
import { TodayEntriesComponent } from './components/today-entries/today-entries.component';
import { ProductListComponent } from './components/product-list/product-list.component';
import { ProductUpdateComponent } from './components/crud/product-update/product-update.component';
import { ProductAddComponent } from './components/crud/product-add/product-add.component';
import { TransactionListComponent } from './components/transaction-list/transaction-list.component';
import { MemberBalanceTopupComponent } from './components/member-balance-topup/member-balance-topup.component';
import { ProductSaleComponent } from './components/product-sale/product-sale.component';
import { UpdateBalanceDialogComponent } from './components/update-balance-dialog/update-balance-dialog.component';
import { LoadingSpinnerComponent } from './components/loading-spinner/loading-spinner.component';
import { CompanyUpdateComponent } from './components/crud/company-update/company-update.component';
import { CompanyAdressUpdateComponent } from './components/crud/company-adress-update/company-adress-update.component';
import { CompanyUserUpdateComponent } from './components/crud/company-user-update/company-user-update.component';
import { UserCompanyUpdateComponent } from './components/crud/user-company-update/user-company-update.component';
import { OperationClaimComponent } from './components/operation-claim/operation-claim.component';
import { UserOperationClaimComponent } from './components/user-operation-claim/user-operation-claim.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { TransactionPaymentDialogComponent } from './components/transaction-payment-dialog/transaction-payment-dialog.component';
import { DebtPaymentDialogComponent } from './components/debt-payment-dialog/debt-payment-dialog.component';
import { QRCodeComponent } from 'angularx-qrcode';
import { VisitStatsComponent } from './components/visit-stats/visit-stats.component';
import { DevicesComponent } from './components/devices/devices.component';
import { FreezeMembershipDialogComponent } from './components/freeze-membership-dialog/freeze-membership-dialog.component';
import { FrozenMembershipsComponent } from './components/frozen-memberships/frozen-memberships.component';
import { DialogService } from './services/dialog.service';
import { HelpGuideService } from './services/help-guide.service';
import { LicensePackagesListComponent } from './components/license-packages-list/license-packages-list.component';
import { LicensePackageAddEditComponent } from './components/crud/license-package-add-edit/license-package-add-edit.component';
import { LicensePackageAddComponent } from './components/crud/license-package-add/license-package-add.component';
import { UserLicensesListComponent } from './components/user-licenses-list/user-licenses-list.component';
import { LicensePurchaseComponent } from './components/license-purchase/license-purchase.component';
import { ExtendLicenseComponent } from './components/extend-license/extend-license.component';
import { LicenseTransactionsComponent } from './components/license-transactions/license-transactions.component';
import { LicenseDashboardComponent } from './components/license-dashboard/license-dashboard.component';
import { LicenseExpiredComponent } from './components/license-expired/license-expired.component';
import { ExpiredLicensesComponent } from './components/expired-licenses/expired-licenses.component';
import { RegisterComponent } from './components/register/register.component'; // Import the RegisterComponent
import { RegisterAdminComponent } from './components/register-admin/register-admin.component';
import { ChangePasswordComponent } from './components/change-password/change-password.component';
import { CompanyContextService } from './services/company-context.service';
import { BirthdayPanelComponent } from './components/birthday-panel/birthday-panel.component';
import { ExpenseManagementComponent } from './components/expense-management/expense-management.component'; // Eklendi
import { ExpenseDialogComponent } from './components/expense-dialog/expense-dialog.component'; // Eklendi
import { ProfileComponent } from './components/profile/profile.component';
import { MyQRComponent } from './components/my-qr/my-qr.component';
import { RateLimitTestComponent } from './components/rate-limit-test/rate-limit-test.component';
import { CacheAdminComponent } from './components/cache-admin/cache-admin.component';
import { ExerciseListComponent } from './components/exercise-list/exercise-list.component';
import { ExerciseAddModalComponent } from './components/exercise-add-modal/exercise-add-modal.component';
import { WorkoutProgramListComponent } from './components/workout-programs/workout-program-list.component';
import { WorkoutProgramAddComponent } from './components/workout-programs/workout-program-add.component';
import { WorkoutProgramDayModalComponent } from './components/workout-programs/workout-program-day-modal.component';
import { ExerciseSelectionModalComponent } from './components/workout-programs/exercise-selection-modal.component';
import { WorkoutProgramEditComponent } from './components/workout-programs/workout-program-edit.component';
import { WorkoutProgramDetailComponent } from './components/workout-programs/workout-program-detail.component';
import { MemberWorkoutAssignmentsComponent } from './components/member-workout-assignments/member-workout-assignments.component';
import { MemberWorkoutAssignModalComponent } from './components/member-workout-assignments/member-workout-assign-modal.component';
import { MemberDeleteDialogComponent } from './components/member-delete-dialog/member-delete-dialog.component';
import { MembershipSelectionDialogComponent } from './components/membership-selection-dialog/membership-selection-dialog.component';

registerLocaleData(localeTr);

// Özel Tarih Formatları Tanımı (Noktalı ayırıcıyı deneyelim)
export const TR_DATE_FORMATS = {
  parse: {
    dateInput: 'DD.MM.YYYY', // Kullanıcının girdiği format (noktalı)
  },
  display: {
    dateInput: 'DD.MM.YYYY', // Input içinde gösterilecek format (noktalı)
    monthYearLabel: 'MMM YYYY', // Ay/Yıl seçici etiketi
    dateA11yLabel: 'LL', // Erişilebilirlik etiketi
    monthYearA11yLabel: 'MMMM YYYY', // Erişilebilirlik etiketi
  },
};

@NgModule({
  declarations: [
    AppComponent,
    CompanyuserComponent,
    SidebarComponent,
    CompanySelectorComponent,
    MemberComponent,
    CompanyUserDetailsComponent,
    CompanyUserDetailDialogComponent,
    MembershiptypeComponent,
    MemberentryexithistoryComponent,
    MemberRemainingDayComponent,
    CityComponent,
    FilterPipePipe,
    MemberFilterPipePipe,
    MemberFilterComponent,
    AllmemberfilterpipePipe,
    ActiveMembersPipe,
    PaymentHistoryComponent,
    MemberAddComponent,
    MembershiptypeAddComponent,
    MembershipAddComponent,
    PaymenthistoryfilterPipePipe,
    MemberUpdateComponent,
    MembershiptypeUpdateComponent,
    MembershipUpdateComponent,
    LoginComponent,
    RegisterComponent, // Add the RegisterComponent here
    RegisterAdminComponent,
    ChangePasswordComponent,
    AppUnauthorizedComponent,
    MemberQRCodeComponent,
    DebtorMemberComponent,
    ConfirmationDialogComponent,
    TodayEntriesComponent,
    ProductListComponent,
    ProductUpdateComponent,
    ProductAddComponent,
    TransactionListComponent,
    MemberBalanceTopupComponent,
    ProductSaleComponent,
    UpdateBalanceDialogComponent,
    LoadingSpinnerComponent,
    CompanyUpdateComponent,
    CompanyAdressUpdateComponent,
    CompanyUserUpdateComponent,
    UserCompanyUpdateComponent,
    OperationClaimComponent,
    UserOperationClaimComponent,
    TransactionPaymentDialogComponent,
    DebtPaymentDialogComponent,
    DevicesComponent,
    FreezeMembershipDialogComponent,
    FrozenMembershipsComponent,
    LicensePackagesListComponent,
    LicensePackageAddEditComponent,
    LicensePackageAddComponent,
    UserLicensesListComponent,
    LicensePurchaseComponent,
    ExtendLicenseComponent,
    LicenseTransactionsComponent,
    LicenseDashboardComponent,
    LicenseExpiredComponent,
    ExpiredLicensesComponent,
    BirthdayPanelComponent,
    MemberDetailDialogComponent,
    CompanyUnifiedAddComponent,
    ExpenseManagementComponent, // Eklendi
    ExpenseDialogComponent, // Eklendi
    ProfileComponent,
    MyQRComponent,
    RateLimitTestComponent,
    CacheAdminComponent,
    ExerciseListComponent,
    ExerciseAddModalComponent,
    WorkoutProgramListComponent,
    WorkoutProgramAddComponent,
    WorkoutProgramDayModalComponent,
    ExerciseSelectionModalComponent,
    WorkoutProgramEditComponent,
    WorkoutProgramDetailComponent,
    MemberWorkoutAssignmentsComponent,
    MemberWorkoutAssignModalComponent,
    MemberDeleteDialogComponent,
    MembershipSelectionDialogComponent,
    DeletedCompaniesComponent,
    HelpDialogComponent,
    HelpButtonComponent
  ],
  imports: [
    BrowserModule,
    CommonModule, // CommonModule buraya eklendi
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    AutoCompleteModule,
    ToastrModule.forRoot({
        positionClass: 'toast-bottom-right',
    }),
    BrowserAnimationsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatOptionModule,
    MatSlideToggleModule,
    NgxPaginationModule,
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    FontAwesomeModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatTableModule,
    MatSortModule,
    NgxPaginationModule,
    QRCodeComponent,
    VisitStatsComponent
],
  providers: [
    DialogService,
    HelpGuideService,
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi(), withFetch()),
    { provide: LOCALE_ID, useValue: 'tr-TR' },
    { provide: MAT_DATE_LOCALE, useValue: 'tr-TR' },
    { provide: MAT_DATE_FORMATS, useValue: TR_DATE_FORMATS },
    CompanyContextService
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
