<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Günlük girişler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <!-- Dashboard Header - Günlük Görünüm -->
    <div class="dashboard-header mb-4" *ngIf="!isSearched">
      <div class="modern-card visitor-summary-card">
        <div class="card-body text-center">
          <div class="visitor-icon">
            <i class="fas fa-users"></i>
          </div>
          <h5 class="card-title">Günlük Toplam Ziyaretçi</h5>
          <div class="visitor-count">{{ getTotalVisitorsToday() }}</div>
          <p class="date-display">{{ appliedDate | date : "dd/MM/yyyy" }}</p>
        </div>
      </div>
    </div>

    <!-- Dashboard Header - Üye Geçmişi Görünümü -->
    <div class="dashboard-header mb-4" *ngIf="isSearched && selectedMember">
      <div class="modern-card visitor-summary-card">
        <div class="card-body text-center">
          <div class="visitor-icon">
            <i class="fas fa-user-clock"></i>
          </div>
          <h5 class="card-title">{{ selectedMember.name }}</h5>
          <div class="visitor-count">{{ paginatedEntries.totalCount }}</div>
          <p class="date-display">
            {{ appliedSearchDate ? ('Seçilen Tarihteki Girişler') : 'Toplam Giriş Sayısı' }}
          </p>
          <small class="text-light" *ngIf="appliedSearchDate">
            {{ appliedSearchDate | date : "dd/MM/yyyy" }}
          </small>
        </div>
      </div>
    </div>

    <!-- Arama ve Filtreleme -->
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center gap-2">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Arama ve Filtreleme
          </h5>

          <!-- Help Button -->
          <app-help-button
            guideId="todayentries"
            position="inline"
            size="small"
            tooltip="Bu panel hakkında yardım al">
          </app-help-button>
        </div>
      </div>
      <div class="modern-card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">
                <i class="fas fa-search me-2"></i>
                Üye Ara
              </label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input
                  type="text"
                  class="form-control"
                  [matAutocomplete]="auto"
                  [formControl]="memberControl"
                  placeholder="İsim veya telefon numarası ile arama yapın..."
                />
              </div>
              <mat-autocomplete
                #auto="matAutocomplete"
                [displayWith]="displayMember"
              >
                <mat-option
                  *ngFor="let member of filteredMembers | async"
                  [value]="member"
                >
                  {{ member.name }} - {{ member.phoneNumber }}
                </mat-option>
              </mat-autocomplete>
            </div>
          </div>

          <div class="col-md-6" *ngIf="!isSearched">
            <div class="mb-3">
              <label class="form-label">
                <i class="fas fa-calendar-alt me-2"></i>
                Tarih Seçin
              </label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                <input
                  type="date"
                  id="dateFilter"
                  class="form-control"
                  [(ngModel)]="selectedDate"
                  (ngModelChange)="onDateChange()"
                />
              </div>
            </div>
          </div>

          <!-- Üye seçildiğinde tarih filtresi -->
          <div class="col-md-6" *ngIf="isSearched">
            <div class="mb-3">
              <label class="form-label">
                <i class="fas fa-calendar-alt me-2"></i>
                Tarih Filtresi (Opsiyonel)
              </label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                <input
                  type="date"
                  class="form-control"
                  [(ngModel)]="memberSearchDate"
                  (ngModelChange)="onMemberSearchDateChange()"
                  placeholder="Tüm geçmiş için boş bırakın"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  *ngIf="memberSearchDate"
                  (click)="clearMemberSearchDate()"
                  title="Tarih filtresini temizle">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <small class="form-text text-muted">
                <i class="fas fa-info-circle me-1"></i>
                {{ memberSearchDate ? 'Tarih seçildi - Aramak için "Ara" butonuna basın' : 'Tüm giriş geçmişi gösteriliyor' }}
              </small>
            </div>
          </div>
        </div>

        <!-- Aktif Filtreler (Üye seçildiğinde) -->
        <div class="row mt-3" *ngIf="isSearched">
          <div class="col-12">
            <div class="alert alert-light border">
              <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="d-flex align-items-center flex-wrap">
                  <span class="me-2 mb-1"><strong>Aktif Filtreler:</strong></span>
                  <span class="badge bg-primary me-2 mb-1">
                    <i class="fas fa-user me-1"></i>
                    {{ selectedMember?.name }}
                  </span>
                  <span class="badge bg-info me-2 mb-1" *ngIf="appliedSearchDate">
                    <i class="fas fa-calendar me-1"></i>
                    {{ appliedSearchDate | date : "dd/MM/yyyy" }}
                  </span>
                  <span class="badge bg-warning me-2 mb-1" *ngIf="memberSearchDate && !appliedSearchDate">
                    <i class="fas fa-clock me-1"></i>
                    Tarih Seçildi ("Ara" Butonuna Basın)
                  </span>
                  <span class="badge bg-success me-2 mb-1" *ngIf="!memberSearchDate && !appliedSearchDate">
                    <i class="fas fa-history me-1"></i>
                    Tüm Geçmiş
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex gap-2 mt-3">
          <button
            *ngIf="shouldShowSearchButton()"
            class="modern-btn modern-btn-primary"
            type="button"
            [disabled]="isSearching"
            (click)="searchMember()"
          >
            <span *ngIf="!isSearching">
              <i class="fas fa-search me-2"></i>
              Ara
            </span>
            <span *ngIf="isSearching">
              <i class="fas fa-spinner fa-spin me-2"></i>
              Aranıyor...
            </span>
          </button>

          <button
            class="modern-btn modern-btn-secondary"
            type="button"
            *ngIf="memberControl.value || selectedDate !== initialDate"
            (click)="clearSearch()"
          >
            <i class="fas fa-times me-2"></i>
            Temizle
          </button>
        </div>
      </div>
    </div>

    <!-- Giriş/Çıkış Tablosu -->
    <div class="modern-card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-list me-3 ms-3"></i>
            {{ getPageTitle() }}
          </h5>
          <small class="text-muted" *ngIf="isSearched">
            <i class="fas fa-info-circle me-1"></i>
            {{ paginatedEntries.totalCount }} kayıt bulundu
          </small>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="modern-table">
            <thead>
              <tr>
                <th>Üye Adı</th>
                <th>Telefon</th>
                <th>Giriş Tarihi</th>
                <th>Giriş Saati</th>
                <th>Çıkış Saati</th>
                <th>Spor Süresi</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let entry of entries" [ngClass]="{'active-entry': !entry.exitTime}">
                <td>
                  <div class="member-info">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(entry.name)">
                      {{ getInitials(entry.name) }}
                    </div>
                    <div>
                      {{ entry.name }}
                      <span
                        class="modern-badge ms-2"
                        *ngIf="!isSearched"
                        [ngClass]="{
                          'modern-badge-danger': entry.remainingDays <= 7,
                          'modern-badge-success': entry.remainingDays >= 8
                        }"
                      >
                        {{ entry.remainingDays }} Gün
                      </span>
                    </div>
                  </div>
                </td>
                <td>{{ entry.phoneNumber }}</td>
                <td>{{ entry.entryTime | date : "dd/MM/yyyy" }}</td>
                <td>{{ entry.entryTime | date : "HH:mm:ss" }}</td>
                <td>
                  <span *ngIf="entry.exitTime && !shouldShowQRWarning(entry)">
                    {{ entry.exitTime | date : "HH:mm:ss" }}
                  </span>
                  <span *ngIf="shouldShowQRWarning(entry)" class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i> Çıkış Yapılmadı
                  </span>
                  <span *ngIf="!entry.exitTime" class="modern-badge modern-badge-success">
                    <i class="fas fa-running me-1"></i> Aktif
                  </span>
                </td>
                <td>
                  <span [ngClass]="{'text-success': getDurationClass(entry) === 'success',
                                    'text-warning': getDurationClass(entry) === 'warning',
                                    'text-danger': getDurationClass(entry) === 'danger'}">
                    {{
                      entry.exitTime
                        ? calculateDuration(entry.entryTime, entry.exitTime)
                        : "-"
                    }}
                  </span>
                </td>
              </tr>
              <tr *ngIf="entries.length === 0">
                <td colspan="6" class="text-center py-4">
                  <div class="empty-state">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <h5>Kayıt bulunamadı</h5>
                    <p class="text-muted">Seçilen kriterlere uygun kayıt bulunamadı.</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination Controls -->
        <div class="pagination-container" *ngIf="usePagination && paginatedEntries.totalPages > 1">
          <div class="pagination-info">
            <span class="text-muted">
              Toplam {{ paginatedEntries.totalCount }} kayıttan
              {{ ((paginatedEntries.pageNumber - 1) * paginatedEntries.pageSize) + 1 }}-{{
                Math.min(paginatedEntries.pageNumber * paginatedEntries.pageSize, paginatedEntries.totalCount)
              }} arası gösteriliyor
            </span>
          </div>

          <div class="pagination-controls">
            <!-- Page Size Selector -->
            <div class="page-size-selector">
              <label class="form-label">Sayfa başına:</label>
              <select
                class="form-select form-select-sm"
                [value]="paginatedEntries.pageSize"
                (change)="onPageSizeChange($event)">
                <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
              </select>
            </div>

            <!-- Pagination Navigation -->
            <nav aria-label="Page navigation" *ngIf="paginatedEntries.totalPages > 1">
              <ul class="pagination">
                <li class="page-item" [class.disabled]="paginatedEntries.pageNumber === 1">
                  <a class="page-link" href="javascript:void(0)" (click)="onPageChange(paginatedEntries.pageNumber - 1)"
                     [attr.aria-label]="'Önceki sayfa'">
                    <i class="fas fa-chevron-left"></i>
                  </a>
                </li>
                <li
                  class="page-item"
                  *ngFor="let page of getPaginationRange()"
                  [class.active]="page === paginatedEntries.pageNumber"
                >
                  <a class="page-link" href="javascript:void(0)" (click)="onPageChange(page)"
                     [attr.aria-label]="'Sayfa ' + page">
                    {{ page }}
                  </a>
                </li>
                <li class="page-item" [class.disabled]="paginatedEntries.pageNumber === paginatedEntries.totalPages">
                  <a class="page-link" href="javascript:void(0)" (click)="onPageChange(paginatedEntries.pageNumber + 1)"
                     [attr.aria-label]="'Sonraki sayfa'">
                    <i class="fas fa-chevron-right"></i>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
