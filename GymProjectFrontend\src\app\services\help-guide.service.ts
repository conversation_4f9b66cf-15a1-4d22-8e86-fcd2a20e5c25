// src/app/services/help-guide.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { HelpGuide, HelpCategory, HelpContent, HelpDialogData } from '../models/help-guide.model';
import { HelpDialogComponent } from '../components/help-dialog/help-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class HelpGuideService {
  private guides: Map<string, HelpGuide> = new Map();

  constructor(private dialog: MatDialog) {
    this.initializeGuides();
  }

  private initializeGuides(): void {
    // Müşteri Yönetimi Rehberleri
    this.addGuide({
      id: 'allmembers',
      title: 'Bütün Üyeler',
      description: 'Spor salonunuzdaki tüm üyeleri görüntüleyin ve yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-users',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu panel spor salonunuzdaki tüm kayıtlı üyeleri görüntülemenizi sağlar. Aktif, pasif ve dondurulmuş üyelikleri tek bir yerden takip edebilir, arama yapabilir ve üye detaylarını yönetebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Tüm üyelerin listesi ve durumları',
            'Gelişmiş arama ve filtreleme',
            'Üye detay bilgileri görüntüleme',
            'Üye düzenleme ve güncelleme',
            'Üyelik durumu takibi',
            'Toplam üye sayısı istatistiği'
          ]
        },
        {
          type: 'steps',
          content: [
            'Arama kutusuna üye adı, telefon veya TC kimlik no yazın',
            'Listeden üyeyi bulun ve detaylarını görüntüleyin',
            'Gerektiğinde üye bilgilerini düzenleyin',
            'Üyelik durumunu kontrol edin',
            'Filtreleme seçeneklerini kullanarak kategorilere ayırın'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Arama kutusuna üye adı, telefon numarası veya TC kimlik numarası yazarak hızlıca üye bulabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'memberfilter',
      title: 'Aktif Üyeler',
      description: 'Aktif üyeleri filtreleyin ve detaylı analizler yapın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-check',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Bu panel aktif üyeleri görüntülemenizi ve çeşitli kriterlere göre filtrelemenizi sağlar. Cinsiyet, yaş grubu ve üyelik türü gibi filtrelerle detaylı analizler yapabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif üye listesi ve istatistikleri',
            'Cinsiyet bazlı filtreleme (Erkek/Kadın)',
            'Yaş grubu analizleri',
            'Üyelik türü kategorileri',
            'Gelişmiş arama özellikleri',
            'Üye detay bilgileri ve düzenleme'
          ]
        },
        {
          type: 'steps',
          content: [
            'Sol panelden istediğiniz filtreleri seçin',
            'Cinsiyet, yaş grubu veya üyelik türü filtrelerini uygulayın',
            'Sağ panelde filtrelenmiş üye listesini görüntüleyin',
            'Arama kutusunu kullanarak spesifik üyeleri bulun',
            'Üye detaylarını incelemek için üye adına tıklayın'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Filtreler kombinlenebilir. Örneğin sadece kadın üyeler ve 25-35 yaş arası gibi çoklu filtreleme yapabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // E-Money Rehberleri
    this.addGuide({
      id: 'memberbalancetopup',
      title: 'Bakiye Yükle - Düşür',
      description: 'Üye bakiyelerini yönetin ve işlem geçmişini takip edin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-money-bill-wave',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin dijital cüzdanlarına para yükleyebilir veya düşürebilirsiniz. Tüm işlemler otomatik olarak kaydedilir.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (TC kimlik no, telefon veya ad ile arama)',
            'İşlem tipini seçin (Yükleme veya Düşürme)',
            'Tutarı girin',
            'Açıklama ekleyin (isteğe bağlı)',
            'İşlemi onaylayın'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Büyük miktarlarda bakiye yüklerken mutlaka açıklama ekleyin. Bu daha sonra raporlama için önemlidir.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Antrenman Rehberleri
    this.addGuide({
      id: 'exercises',
      title: 'Egzersiz Listesi',
      description: 'Spor salonunuz için egzersiz veritabanını yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-list-ul',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Antrenman programları oluşturmak için kullanacağınız egzersizleri bu panelden yönetebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Sistem egzersizleri (hazır egzersiz veritabanı)',
            'Salon egzersizleri (kendi eklediğiniz egzersizler)',
            'Egzersiz kategorileri',
            'Hedef kas grupları'
          ]
        },
        {
          type: 'steps',
          content: [
            'Yeni egzersiz eklemek için "Egzersiz Ekle" butonuna tıklayın',
            'Egzersiz adını girin',
            'Kategori seçin',
            'Hedef kas gruplarını belirleyin',
            'Açıklama ekleyin',
            'Kaydedin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'frozen-memberships',
      title: 'Dondurulmuş Üyelikler',
      description: 'Geçici olarak dondurulmuş üyelikleri yönetin ve takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-snowflake',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin geçici olarak spor salonunu kullanamayacağı durumlarda (hastalık, seyahat, vb.) üyeliklerini dondurabilirsiniz. Bu panel dondurulmuş üyelikleri ve dondurma geçmişini görüntüler.'
        },
        {
          type: 'list',
          content: [
            'Aktif dondurulmuş üyelikler listesi',
            'Dondurma tarihi ve süresi bilgileri',
            'Dondurma geçmişi ve işlem kayıtları',
            'Üye iletişim bilgileri',
            'Dondurma sebebi ve açıklamaları',
            'Otomatik süre uzatma hesaplamaları'
          ]
        },
        {
          type: 'steps',
          content: [
            'Toggle butonuyla dondurulmuş üyelikler ve geçmiş arasında geçiş yapın',
            'Dondurulmuş üyeliklerin listesini görüntüleyin',
            'Dondurulan tarihi, süreyi ve sebebini kontrol edin',
            'Gerektiğinde üye ile iletişime geçin',
            'Dondurma süresi bittiğinde üyeliği tekrar aktifleştirin'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Dondurulan süre otomatik olarak üyelik bitiş tarihine eklenir. Dondurma işlemi geri alınamaz.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Dondurma işlemlerini düzenli takip ederek üye memnuniyetini artırabilir ve üye kaybını önleyebilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'memberremainingday',
      title: 'Üyelik Bitişi Yaklaşanlar',
      description: 'Üyeliği 7 gün ve altında olan üyeleri takip edin ve yenileme yapın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hourglass-end',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Üyeliği 7 gün ve altında kalan üyeleri bu panelden takip edebilirsiniz. Kritik dönemdeki üyelerle hızlıca iletişime geçerek üye kaybını önleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üyeliği 7 gün ve altında kalan üyeler',
            'Günlük kalan süre sayacı',
            'Üye iletişim bilgileri',
            'Son ödeme tarihleri ve tutarları',
            'Üyelik türü ve paket bilgileri',
            'Hızlı arama ve filtreleme'
          ]
        },
        {
          type: 'steps',
          content: [
            'Listeyi günlük olarak kontrol edin',
            'Kalan günü az olan üyelerle öncelikli iletişime geçin',
            'Üyelik yenileme tekliflerini hazırlayın',
            'Özel indirimler veya kampanyalar sunun',
            'Yenileme işlemlerini hızlıca tamamlayın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Bu liste kritik öneme sahiptir. Günlük takip yapılmazsa üye kayıpları yaşanabilir.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Üyelik bitmeden 3-5 gün önce hatırlatma yaparak üye memnuniyetini artırın ve yenileme oranını yükseltin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // E-Money Rehberleri devam
    this.addGuide({
      id: 'products',
      title: 'Ürün Ekle',
      description: 'Spor salonunuzda satılacak ürünleri yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-basket',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzda satacağınız ürünleri (protein, spor giyim, aksesuar vb.) bu panelden ekleyip yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Ürün adını girin',
            'Satış fiyatını belirleyin',
            'Stok miktarını girin (isteğe bağlı)',
            'Ürün açıklaması ekleyin',
            'Ürünü kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Ürün fiyatlarını düzenli olarak güncelleyin. Stok takibi yaparak tükenmek üzere olan ürünleri önceden tespit edin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'product-sale',
      title: 'Ürün Sat',
      description: 'Üyelerinize ürün satışı yapın ve ödemeleri yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-cart',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Üyelerinize ürün satışı yapabilir, nakit veya dijital bakiye ile ödeme alabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (TC kimlik no, telefon veya ad ile arama)',
            'Satılacak ürünü seçin',
            'Miktarı belirleyin',
            'Ödeme yöntemini seçin (Nakit/Bakiye)',
            'Satışı tamamlayın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Bakiye ile ödeme yapılırken üyenin yeterli bakiyesi olduğundan emin olun. Yetersiz bakiye durumunda işlem gerçekleşmez.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'transactions',
      title: 'İşlem Takibi',
      description: 'Tüm finansal işlemleri takip edin ve raporlayın',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-exchange-alt',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzda gerçekleşen tüm finansal işlemleri (bakiye yükleme, ürün satışı, üyelik ödemeleri) bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Bakiye yükleme işlemleri',
            'Ürün satış işlemleri',
            'Üyelik ödeme işlemleri',
            'İşlem tarihleri ve tutarları',
            'İşlem yapan personel bilgisi'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Günlük, haftalık ve aylık raporlar alarak gelir analizinizi yapabilirsiniz. Filtreleme seçeneklerini kullanarak detaylı analizler yapın.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Antrenman Rehberleri devam
    this.addGuide({
      id: 'workout-programs',
      title: 'Antrenman Programları',
      description: 'Üyeleriniz için antrenman programları oluşturun ve yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-dumbbell',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Üyeleriniz için kişiselleştirilmiş antrenman programları oluşturabilir ve mevcut programları yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Program adını belirleyin',
            'Program süresini seçin (hafta sayısı)',
            'Günlük antrenmanları ekleyin',
            'Her gün için egzersizleri seçin',
            'Set, tekrar ve ağırlık bilgilerini girin',
            'Programı kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Farklı seviyeler için (başlangıç, orta, ileri) ayrı programlar oluşturun. Bu şekilde üyelerinize daha uygun programlar atayabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'member-workout-assignments',
      title: 'Program Atamaları',
      description: 'Üyelere antrenman programları atayın ve takip edin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-plus',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Oluşturduğunuz antrenman programlarını üyelerinize atayabilir ve ilerlemelerini takip edebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin',
            'Uygun antrenman programını seçin',
            'Başlangıç tarihini belirleyin',
            'Hedefleri ve notları ekleyin',
            'Atamayı kaydedin'
          ]
        },
        {
          type: 'list',
          content: [
            'Aktif program atamaları',
            'Program başlangıç tarihleri',
            'Üye ilerlemeleri',
            'Tamamlanan antrenmanlar'
          ]
        }
      ]
    });

    // Owner/Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'license-dashboard',
      title: 'Ana Panel',
      description: 'Sistem geneli istatistikleri ve önemli bilgileri görüntüleyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-tachometer-alt',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistem yöneticisi olarak tüm salonlarınızın genel durumunu, lisans bilgilerini ve önemli istatistikleri bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam salon sayısı',
            'Aktif lisans sayısı',
            'Yakında dolacak lisanslar',
            'Günlük/aylık gelir istatistikleri',
            'Sistem kullanım oranları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Lisansı dolmak üzere olan salonları düzenli olarak takip edin. Lisans süresi dolduğunda salon sistemi kullanamaz.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'company/unified-add',
      title: 'Yeni Salon Ekle',
      description: 'Sisteme yeni spor salonu ekleyin ve lisans atayın',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-plus-square',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sisteme yeni bir spor salonu ekleyebilir, salon sahibini tanımlayabilir ve lisans paketini atayabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Salon bilgilerini girin (ad, adres, telefon)',
            'Salon sahibinin bilgilerini ekleyin',
            'Lisans paketini seçin',
            'Lisans süresini belirleyin',
            'Ödeme bilgilerini girin',
            'Salonu sisteme kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Salon eklerken tüm bilgileri eksiksiz doldurun. Bu bilgiler daha sonra raporlama ve iletişim için kullanılacaktır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Sistem Yönetimi Rehberleri
    this.addGuide({
      id: 'roles',
      title: 'Rol Yönetimi',
      description: 'Sistem rollerini tanımlayın ve yetkileri yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-shield',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistemde kullanılacak rolleri tanımlayabilir ve her role hangi yetkilerin verileceğini belirleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Owner: Sistem yöneticisi (tüm yetkiler)',
            'Admin: Salon yöneticisi (salon işlemleri)',
            'Member: Üye (sınırlı yetkiler)',
            'Özel roller tanımlayabilirsiniz'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Rol yetkilerini değiştirirken dikkatli olun. Yanlış yetki ataması güvenlik sorunlarına neden olabilir.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'user-roles',
      title: 'Kullanıcı Rolleri',
      description: 'Kullanıcılara rol atayın ve yetkileri düzenleyin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-users-cog',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sistem kullanıcılarına roller atayabilir ve mevcut rol atamalarını yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Rol atamak istediğiniz kullanıcıyı seçin',
            'Uygun rolü seçin',
            'Atamayı kaydedin',
            'Değişikliklerin etkili olduğunu kontrol edin'
          ]
        }
      ]
    });

    // Genel Rehberler
    this.addGuide({
      id: 'todayentries',
      title: 'Günlük Giriş-Çıkışlar',
      description: 'Bugün spor salonuna giriş yapan üyeleri görüntüleyin',
      category: HelpCategory.GENERAL,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-door-open',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bugün spor salonunuza giriş yapan üyelerin listesini görüntüleyebilir ve giriş-çıkış saatlerini takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Günlük giriş yapan üye sayısı',
            'Giriş ve çıkış saatleri',
            'Üye bilgileri',
            'QR kod ile giriş kayıtları'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Bu panel ana sayfa olarak ayarlanmıştır. Günlük operasyonlarınızı buradan takip edebilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Müşteri Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'debtormember',
      title: 'Borçlu Üyeler',
      description: 'Ödeme borcu olan üyeleri takip edin ve borç tahsilat işlemlerini yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hand-holding-usd',
      priority: 5,
      content: [
        {
          type: 'text',
          content: 'Üyelik ödemelerini zamanında yapmayan, eksik ödeme yapan veya ödeme planı olan üyeleri bu panelden takip edebilir ve borç tahsilat işlemlerini yönetebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam borçlu üye sayısı ve borç tutarı',
            'Üye bazlı borç detayları',
            'Kalan borç miktarları',
            'Son ödeme tarihleri ve geçmişi',
            'Üye iletişim bilgileri',
            'Hızlı ödeme alma işlemleri'
          ]
        },
        {
          type: 'steps',
          content: [
            'Borçlu üyeler listesini günlük kontrol edin',
            'Yüksek borçlu üyelerle öncelikli iletişime geçin',
            'Ödeme planları ve taksit seçenekleri sunun',
            'Kısmi ödemeleri kabul ederek borcu azaltın',
            'Ödeme alındıktan sonra borç durumunu güncelleyin'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Uzun süreli borçlu üyeler salon operasyonlarını olumsuz etkileyebilir. Düzenli takip yapın.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Borçlu üyelerle nazik ama kararlı iletişim kurun. Ödeme kolaylıkları sunarak tahsilat oranını artırın.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'birthdays',
      title: 'Doğum Günleri',
      description: 'Üyelerinizin doğum günlerini takip edin ve kutlayın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-birthday-cake',
      priority: 6,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin doğum günlerini takip ederek özel günlerinde onları kutlayabilir ve müşteri memnuniyetini artırabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Bugün doğum günü olan üyeleri görüntüleyin',
            'Yaklaşan doğum günlerini kontrol edin',
            'Üyelere doğum günü mesajı gönderin',
            'Özel indirimler veya hediyeler sunun'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Doğum günü kutlamaları üye sadakatini artırır ve pozitif deneyim yaratır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'paymenthistory',
      title: 'Kasa Raporu',
      description: 'Detaylı ödeme geçmişi ve kasa raporlarını görüntüleyin ve analiz edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-cash-register',
      priority: 7,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzun tüm ödeme işlemlerini, gelir analizlerini ve kasa raporlarını bu panelden detaylıca takip edebilirsiniz. Tarih aralığı seçerek özelleştirilmiş raporlar oluşturabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Nakit, kredi kartı ve havale/EFT ödemeleri',
            'Tarih aralığına göre filtreleme',
            'Üye bazlı ödeme geçmişi',
            'Ödeme yöntemlerine göre dağılım grafikleri',
            'Excel formatında rapor dışa aktarma',
            'Günlük, haftalık, aylık toplam analizleri'
          ]
        },
        {
          type: 'steps',
          content: [
            'Tarih aralığını seçin (başlangıç-bitiş)',
            'Üye bazlı arama yapmak için üye adını girin',
            'Ödeme türlerine göre filtreleme yapın',
            'Toplam tutarları ve dağılımları inceleyin',
            'Detaylı analiz için Excel raporu alın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Kasa raporları finansal analiziniz için kritik öneme sahiptir. Günlük olarak kontrol edin ve kayıtları düzenli tutun.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Aylık raporları Excel\'e aktararak muhasebe işlemlerinizi kolaylaştırabilir ve trend analizleri yapabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'expenses',
      title: 'Gider Yönetimi',
      description: 'Spor salonu giderlerinizi kaydedin ve takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-receipt',
      priority: 8,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzun tüm giderlerini kategorilere ayırarak kaydedin ve raporlayın.'
        },
        {
          type: 'steps',
          content: [
            'Gider kategorisini seçin',
            'Gider tutarını girin',
            'Açıklama ekleyin',
            'Fatura/fiş bilgilerini kaydedin',
            'Gideri onaylayın'
          ]
        },
        {
          type: 'list',
          content: [
            'Kira giderleri',
            'Elektrik, su, doğalgaz',
            'Personel maaşları',
            'Ekipman bakım giderleri',
            'Temizlik malzemeleri'
          ]
        }
      ]
    });

    // CRUD İşlemleri Rehberleri
    this.addGuide({
      id: 'membershiptype-add',
      title: 'Üyelik Türü Ekleme',
      description: 'Yeni üyelik türleri oluşturun ve fiyatlandırma yapın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-plus-circle',
      priority: 9,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuz için yeni üyelik türleri oluşturabilir, branş bazlı fiyatlandırma yapabilir ve üyelik paketlerini özelleştirebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Branş seçimi (Fitness, Boks, MMA, vb.)',
            'Üyelik türü tanımlama',
            'Süre belirleme (gün bazında)',
            'Fiyat belirleme',
            'Özel açıklamalar ekleme',
            'Mevcut türlerin listesi ve yönetimi'
          ]
        },
        {
          type: 'steps',
          content: [
            'Branş seçin (Fitness, Boks, Muay Thai, vb.)',
            'Üyelik türü adını girin (örn: Aylık, 3 Aylık)',
            'Süreyi gün cinsinden belirleyin',
            'Fiyatı Türk Lirası olarak girin',
            'İsteğe bağlı açıklama ekleyin',
            'Kaydet butonuna tıklayarak üyelik türünü oluşturun'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Farklı branşlar için ayrı fiyatlandırma yapabilirsiniz. Popüler paketler için cazip fiyatlar belirleyin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'member-add',
      title: 'Yeni Üye Ekleme',
      description: 'Sisteme yeni üye kaydı oluşturun ve bilgilerini girin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-plus',
      priority: 10,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuza yeni üye kaydı oluşturabilir, kişisel bilgilerini girebilir ve üyelik işlemlerini başlatabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Kişisel bilgiler (Ad, soyad, TC kimlik)',
            'İletişim bilgileri (Telefon, e-posta)',
            'Adres bilgileri',
            'Doğum tarihi ve cinsiyet',
            'Acil durum iletişim bilgileri',
            'Form tamamlanma durumu takibi'
          ]
        },
        {
          type: 'steps',
          content: [
            'Ad, soyad ve TC kimlik numarasını girin',
            'Telefon numarası ve e-posta adresini ekleyin',
            'Doğum tarihi ve cinsiyeti seçin',
            'Adres bilgilerini doldurun',
            'Acil durum iletişim kişisini belirleyin',
            'Tüm bilgileri kontrol edip kaydedin'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: TC kimlik numarası benzersiz olmalıdır. Aynı TC ile kayıtlı üye varsa sistem uyarı verecektir.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Form doldurma ilerlemesi üst kısımda gösterilir. Eksik alanları tamamlayarak kayıt işlemini hızlandırın.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'membership-add',
      title: 'Üyelik Süresi Ekleme',
      description: 'Mevcut üyelere yeni üyelik süresi ekleyin ve ödemeleri yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-id-card',
      priority: 11,
      content: [
        {
          type: 'text',
          content: 'Kayıtlı üyelere yeni üyelik süresi ekleyebilir, ödeme işlemlerini gerçekleştirebilir ve üyelik durumlarını güncelleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üye seçimi ve arama',
            'Üyelik türü seçimi',
            'Başlangıç tarihi belirleme',
            'Ödeme tutarı ve yöntemi',
            'İndirim ve kampanya uygulamaları',
            'Otomatik süre hesaplama'
          ]
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (TC kimlik, telefon veya ad ile arama)',
            'Üyelik türünü seçin (branş ve süre)',
            'Başlangıç tarihini belirleyin',
            'Ödeme tutarını kontrol edin',
            'İndirim varsa uygulayın',
            'Ödeme yöntemini seçip işlemi tamamlayın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Üyelik türü seçilmeden işlem yapılamaz. Önce üyelik türlerini tanımladığınızdan emin olun.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'tip',
          content: 'İpucu: Form ilerlemesi takip edilir. Tüm alanları doldurarak hızlı üyelik işlemi gerçekleştirin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'companyuserdetails',
      title: 'Salon Sahipleri',
      description: 'Sistem kullanıcılarını ve salon sahiplerini yönetin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-id-card',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sisteme kayıtlı salon sahiplerini ve yöneticilerini görüntüleyebilir, bilgilerini düzenleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Salon sahipleri listesi',
            'İletişim bilgileri',
            'Salon bağlantıları',
            'Hesap durumları',
            'Son giriş tarihleri'
          ]
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Detay bilgilerini inceleyin',
            'Gerektiğinde bilgileri güncelleyin',
            'Hesap durumlarını kontrol edin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'license-transactions',
      title: 'Satış Raporları',
      description: 'Lisans satış raporlarını görüntüleyin ve analiz edin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-receipt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistem genelindeki lisans satışlarını, gelir analizlerini ve ödeme raporlarını bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam satış tutarları',
            'Ödeme yöntemlerine göre dağılım',
            'Tarih aralığına göre filtreleme',
            'Admin bazlı satış raporları',
            'Paket bazlı analizler'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Raporları Excel formatında dışa aktararak detaylı analizler yapabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'deleted-companies',
      title: 'Pasif Salonlar',
      description: 'Silinen salonları görüntüleyin ve geri yükleyin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-trash-restore',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistemden silinen salonları görüntüleyebilir ve gerektiğinde geri yükleyebilirsiniz.'
        },
        {
          type: 'warning',
          content: 'Dikkat: Salon geri yükleme işlemi geri alınamaz. İşlem öncesi emin olun.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'steps',
          content: [
            'Silinen salonlar listesini görüntüleyin',
            'Salon detaylarını inceleyin',
            'Geri yükleme uygunluğunu kontrol edin',
            'Geri yükleme işlemini onaylayın'
          ]
        }
      ]
    });

    // Lisans Yönetimi Rehberleri
    this.addGuide({
      id: 'license-packages-add',
      title: 'Lisans Paketleri',
      description: 'Sistem lisans paketlerini oluşturun ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-box-open',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Farklı salon ihtiyaçları için lisans paketleri oluşturabilir ve mevcut paketleri yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Paket adını belirleyin',
            'Paket özelliklerini tanımlayın',
            'Fiyatlandırmayı ayarlayın',
            'Süre limitlerini belirleyin',
            'Paketi aktifleştirin'
          ]
        },
        {
          type: 'list',
          content: [
            'Temel paket (küçük salonlar)',
            'Standart paket (orta salonlar)',
            'Premium paket (büyük salonlar)',
            'Kurumsal paket (zincir salonlar)'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'user-licenses',
      title: 'Aktif Lisanslar',
      description: 'Sistemdeki aktif lisansları görüntüleyin ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-tag',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistemde aktif olan tüm lisansları görüntüleyebilir, süre uzatabilir veya iptal edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif lisans sayısı',
            'Lisans bitiş tarihleri',
            'Paket türleri',
            'Salon bilgileri',
            'Ödeme durumları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Lisans iptal işlemleri salon operasyonlarını durdurur. Dikkatli olun.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'expired-licenses',
      title: 'Lisansı Dolan Üyeler',
      description: 'Süresi dolan lisansları görüntüleyin ve yenileyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-exclamation-triangle',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Lisans süresi dolan salonları görüntüleyebilir ve lisans yenileme işlemlerini gerçekleştirebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Süresi dolan lisansları listeleyin',
            'Salon sahipleriyle iletişime geçin',
            'Yenileme teklifini hazırlayın',
            'Ödeme alın ve lisansı yenileyin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Lisans bitiş tarihinden önce hatırlatma yaparak müşteri kaybını önleyin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Sistem Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'devices',
      title: 'Aktif Cihazlar',
      description: 'Sisteme bağlı cihazları görüntüleyin ve yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-mobile-alt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sisteme bağlı tüm cihazları (mobil, tablet, bilgisayar) görüntüleyebilir ve güvenlik kontrolü yapabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif cihaz sayısı',
            'Cihaz türleri ve modelleri',
            'Son bağlantı tarihleri',
            'IP adresleri',
            'Kullanıcı bilgileri'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Şüpheli cihazları sistemden çıkararak güvenliği sağlayın.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'cache-admin',
      title: 'Cache Yönetimi',
      description: 'Sistem önbelleğini yönetin ve performansı optimize edin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-database',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistem performansını artırmak için önbellek yönetimi yapabilir ve gerektiğinde temizleyebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Cache durumunu kontrol edin',
            'Bellek kullanımını görüntüleyin',
            'Gerektiğinde cache temizleyin',
            'Performans metriklerini inceleyin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Düzenli cache temizliği sistem performansını artırır ve hata riskini azaltır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });
  }

  private addGuide(guide: HelpGuide): void {
    this.guides.set(guide.id, guide);
  }

  getGuide(id: string): HelpGuide | undefined {
    return this.guides.get(id);
  }

  getGuidesByCategory(category: HelpCategory): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.category === category)
      .sort((a, b) => a.priority - b.priority);
  }

  getGuidesByRole(role: string): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.targetRoles.includes(role))
      .sort((a, b) => a.priority - b.priority);
  }

  openHelpDialog(guideId: string): Observable<any> {
    const guide = this.getGuide(guideId);
    if (!guide) {
      console.warn(`Help guide with id '${guideId}' not found`);
      return new Observable(observer => observer.complete());
    }

    const dialogRef = this.dialog.open(HelpDialogComponent, {
      width: '800px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: { guide, showCloseButton: true } as HelpDialogData,
      disableClose: false,
      position: { top: '30px' },
      panelClass: 'help-dialog-container'
    });

    return dialogRef.afterClosed();
  }

  getAllGuides(): HelpGuide[] {
    return Array.from(this.guides.values())
      .sort((a, b) => a.priority - b.priority);
  }
}
