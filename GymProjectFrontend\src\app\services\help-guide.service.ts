// src/app/services/help-guide.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { HelpGuide, HelpCategory, HelpContent, HelpDialogData } from '../models/help-guide.model';
import { HelpDialogComponent } from '../components/help-dialog/help-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class HelpGuideService {
  private guides: Map<string, HelpGuide> = new Map();

  constructor(private dialog: MatDialog) {
    this.initializeGuides();
  }

  private initializeGuides(): void {
    // Müşteri Yönetimi Rehberleri
    this.addGuide({
      id: 'allmembers',
      title: 'Bütün Üyeler',
      description: 'Spor salonunuzdaki tüm üyeleri görüntüleyin ve yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-list',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu panel spor salonunuzdaki tüm üyeleri görüntülemenizi sağlar. Aktif, pasif ve donmuş üyelikleri tek bir yerden takip edebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Üye listesini görüntülemek için sayfayı açın',
            'Arama kutusunu kullanarak belirli üyeleri bulun',
            'Filtreleme seçenekleriyle üyeleri kategorilere ayırın',
            'Üye detaylarını görmek için üye adına tıklayın',
            'Gerektiğinde üye bilgilerini düzenleyin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Arama kutusuna üye adı, telefon numarası veya TC kimlik numarası yazarak hızlıca üye bulabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'memberfilter',
      title: 'Aktif Üyeler',
      description: 'Sadece aktif üyelikleri olan üyeleri görüntüleyin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-check',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Bu panel sadece aktif üyelikleri olan üyeleri gösterir. Günlük operasyonlarınızda en çok kullanacağınız paneldir.'
        },
        {
          type: 'list',
          content: [
            'Aktif üyelik durumu olan üyeler',
            'Üyelik bitiş tarihleri',
            'Kalan gün sayıları',
            'Ödeme durumları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Bu listede sadece aktif üyelikler görünür. Tüm üyeleri görmek için "Bütün Üyeler" panelini kullanın.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    // E-Money Rehberleri
    this.addGuide({
      id: 'memberbalancetopup',
      title: 'Bakiye Yükle - Düşür',
      description: 'Üye bakiyelerini yönetin ve işlem geçmişini takip edin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-money-bill-wave',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin dijital cüzdanlarına para yükleyebilir veya düşürebilirsiniz. Tüm işlemler otomatik olarak kaydedilir.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (TC kimlik no, telefon veya ad ile arama)',
            'İşlem tipini seçin (Yükleme veya Düşürme)',
            'Tutarı girin',
            'Açıklama ekleyin (isteğe bağlı)',
            'İşlemi onaylayın'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Büyük miktarlarda bakiye yüklerken mutlaka açıklama ekleyin. Bu daha sonra raporlama için önemlidir.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Antrenman Rehberleri
    this.addGuide({
      id: 'exercises',
      title: 'Egzersiz Listesi',
      description: 'Spor salonunuz için egzersiz veritabanını yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-list-ul',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Antrenman programları oluşturmak için kullanacağınız egzersizleri bu panelden yönetebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Sistem egzersizleri (hazır egzersiz veritabanı)',
            'Salon egzersizleri (kendi eklediğiniz egzersizler)',
            'Egzersiz kategorileri',
            'Hedef kas grupları'
          ]
        },
        {
          type: 'steps',
          content: [
            'Yeni egzersiz eklemek için "Egzersiz Ekle" butonuna tıklayın',
            'Egzersiz adını girin',
            'Kategori seçin',
            'Hedef kas gruplarını belirleyin',
            'Açıklama ekleyin',
            'Kaydedin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'frozen-memberships',
      title: 'Dondurulmuş Üyelikler',
      description: 'Geçici olarak dondurulmuş üyelikleri yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-snowflake',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin geçici olarak spor salonunu kullanamayacağı durumlarda üyeliklerini dondurabilirsiniz. Dondurulan süre üyelik bitiş tarihine eklenir.'
        },
        {
          type: 'steps',
          content: [
            'Dondurulmuş üyeliklerin listesini görüntüleyin',
            'Dondurulan tarihi ve süreyi kontrol edin',
            'Gerektiğinde üyeliği tekrar aktifleştirin',
            'Dondurulan sürenin üyelik bitiş tarihine eklendiğini doğrulayın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Üyelik dondurma işlemi geri alınamaz. Dondurulan süre otomatik olarak üyelik bitiş tarihine eklenir.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'memberremainingday',
      title: 'Üyelik Bitişi Yaklaşanlar',
      description: 'Üyeliği bitmek üzere olan üyeleri takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hourglass-end',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Üyeliği 30 gün içinde bitecek olan üyeleri bu panelden takip edebilirsiniz. Proaktif yaklaşım sergileyerek üye kaybını önleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üyeliği 30 gün içinde bitecek üyeler',
            'Kalan gün sayıları',
            'İletişim bilgileri',
            'Son ödeme tarihleri'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Bu listeyi düzenli olarak kontrol ederek üyelerinizle iletişime geçin. Erken hatırlatma üye memnuniyetini artırır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // E-Money Rehberleri devam
    this.addGuide({
      id: 'products',
      title: 'Ürün Ekle',
      description: 'Spor salonunuzda satılacak ürünleri yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-basket',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzda satacağınız ürünleri (protein, spor giyim, aksesuar vb.) bu panelden ekleyip yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Ürün adını girin',
            'Satış fiyatını belirleyin',
            'Stok miktarını girin (isteğe bağlı)',
            'Ürün açıklaması ekleyin',
            'Ürünü kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Ürün fiyatlarını düzenli olarak güncelleyin. Stok takibi yaparak tükenmek üzere olan ürünleri önceden tespit edin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'product-sale',
      title: 'Ürün Sat',
      description: 'Üyelerinize ürün satışı yapın ve ödemeleri yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-cart',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Üyelerinize ürün satışı yapabilir, nakit veya dijital bakiye ile ödeme alabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (TC kimlik no, telefon veya ad ile arama)',
            'Satılacak ürünü seçin',
            'Miktarı belirleyin',
            'Ödeme yöntemini seçin (Nakit/Bakiye)',
            'Satışı tamamlayın'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Bakiye ile ödeme yapılırken üyenin yeterli bakiyesi olduğundan emin olun. Yetersiz bakiye durumunda işlem gerçekleşmez.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'transactions',
      title: 'İşlem Takibi',
      description: 'Tüm finansal işlemleri takip edin ve raporlayın',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-exchange-alt',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzda gerçekleşen tüm finansal işlemleri (bakiye yükleme, ürün satışı, üyelik ödemeleri) bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Bakiye yükleme işlemleri',
            'Ürün satış işlemleri',
            'Üyelik ödeme işlemleri',
            'İşlem tarihleri ve tutarları',
            'İşlem yapan personel bilgisi'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Günlük, haftalık ve aylık raporlar alarak gelir analizinizi yapabilirsiniz. Filtreleme seçeneklerini kullanarak detaylı analizler yapın.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Antrenman Rehberleri devam
    this.addGuide({
      id: 'workout-programs',
      title: 'Antrenman Programları',
      description: 'Üyeleriniz için antrenman programları oluşturun ve yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-dumbbell',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Üyeleriniz için kişiselleştirilmiş antrenman programları oluşturabilir ve mevcut programları yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Program adını belirleyin',
            'Program süresini seçin (hafta sayısı)',
            'Günlük antrenmanları ekleyin',
            'Her gün için egzersizleri seçin',
            'Set, tekrar ve ağırlık bilgilerini girin',
            'Programı kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Farklı seviyeler için (başlangıç, orta, ileri) ayrı programlar oluşturun. Bu şekilde üyelerinize daha uygun programlar atayabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'member-workout-assignments',
      title: 'Program Atamaları',
      description: 'Üyelere antrenman programları atayın ve takip edin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-plus',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Oluşturduğunuz antrenman programlarını üyelerinize atayabilir ve ilerlemelerini takip edebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Üye seçin',
            'Uygun antrenman programını seçin',
            'Başlangıç tarihini belirleyin',
            'Hedefleri ve notları ekleyin',
            'Atamayı kaydedin'
          ]
        },
        {
          type: 'list',
          content: [
            'Aktif program atamaları',
            'Program başlangıç tarihleri',
            'Üye ilerlemeleri',
            'Tamamlanan antrenmanlar'
          ]
        }
      ]
    });

    // Owner/Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'license-dashboard',
      title: 'Ana Panel',
      description: 'Sistem geneli istatistikleri ve önemli bilgileri görüntüleyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-tachometer-alt',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistem yöneticisi olarak tüm salonlarınızın genel durumunu, lisans bilgilerini ve önemli istatistikleri bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam salon sayısı',
            'Aktif lisans sayısı',
            'Yakında dolacak lisanslar',
            'Günlük/aylık gelir istatistikleri',
            'Sistem kullanım oranları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Lisansı dolmak üzere olan salonları düzenli olarak takip edin. Lisans süresi dolduğunda salon sistemi kullanamaz.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'company/unified-add',
      title: 'Yeni Salon Ekle',
      description: 'Sisteme yeni spor salonu ekleyin ve lisans atayın',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-plus-square',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sisteme yeni bir spor salonu ekleyebilir, salon sahibini tanımlayabilir ve lisans paketini atayabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Salon bilgilerini girin (ad, adres, telefon)',
            'Salon sahibinin bilgilerini ekleyin',
            'Lisans paketini seçin',
            'Lisans süresini belirleyin',
            'Ödeme bilgilerini girin',
            'Salonu sisteme kaydedin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Salon eklerken tüm bilgileri eksiksiz doldurun. Bu bilgiler daha sonra raporlama ve iletişim için kullanılacaktır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Sistem Yönetimi Rehberleri
    this.addGuide({
      id: 'roles',
      title: 'Rol Yönetimi',
      description: 'Sistem rollerini tanımlayın ve yetkileri yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-shield',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistemde kullanılacak rolleri tanımlayabilir ve her role hangi yetkilerin verileceğini belirleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Owner: Sistem yöneticisi (tüm yetkiler)',
            'Admin: Salon yöneticisi (salon işlemleri)',
            'Member: Üye (sınırlı yetkiler)',
            'Özel roller tanımlayabilirsiniz'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Rol yetkilerini değiştirirken dikkatli olun. Yanlış yetki ataması güvenlik sorunlarına neden olabilir.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'user-roles',
      title: 'Kullanıcı Rolleri',
      description: 'Kullanıcılara rol atayın ve yetkileri düzenleyin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-users-cog',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sistem kullanıcılarına roller atayabilir ve mevcut rol atamalarını yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Rol atamak istediğiniz kullanıcıyı seçin',
            'Uygun rolü seçin',
            'Atamayı kaydedin',
            'Değişikliklerin etkili olduğunu kontrol edin'
          ]
        }
      ]
    });

    // Genel Rehberler
    this.addGuide({
      id: 'todayentries',
      title: 'Günlük Giriş-Çıkışlar',
      description: 'Bugün spor salonuna giriş yapan üyeleri görüntüleyin',
      category: HelpCategory.GENERAL,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-door-open',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bugün spor salonunuza giriş yapan üyelerin listesini görüntüleyebilir ve giriş-çıkış saatlerini takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Günlük giriş yapan üye sayısı',
            'Giriş ve çıkış saatleri',
            'Üye bilgileri',
            'QR kod ile giriş kayıtları'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Bu panel ana sayfa olarak ayarlanmıştır. Günlük operasyonlarınızı buradan takip edebilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Müşteri Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'debtormember',
      title: 'Borçlu Üyeler',
      description: 'Ödeme borcu olan üyeleri takip edin ve yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hand-holding-usd',
      priority: 5,
      content: [
        {
          type: 'text',
          content: 'Üyelik ödemelerini zamanında yapmayan veya eksik ödeme yapan üyeleri bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Ödeme borcu olan üyeler',
            'Borç miktarları',
            'Son ödeme tarihleri',
            'İletişim bilgileri'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Borçlu üyelerle düzenli iletişim kurarak ödeme planları oluşturun. Bu üye kaybını önler.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'birthdays',
      title: 'Doğum Günleri',
      description: 'Üyelerinizin doğum günlerini takip edin ve kutlayın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-birthday-cake',
      priority: 6,
      content: [
        {
          type: 'text',
          content: 'Üyelerinizin doğum günlerini takip ederek özel günlerinde onları kutlayabilir ve müşteri memnuniyetini artırabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Bugün doğum günü olan üyeleri görüntüleyin',
            'Yaklaşan doğum günlerini kontrol edin',
            'Üyelere doğum günü mesajı gönderin',
            'Özel indirimler veya hediyeler sunun'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Doğum günü kutlamaları üye sadakatini artırır ve pozitif deneyim yaratır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'paymenthistory',
      title: 'Kasa Raporu',
      description: 'Günlük gelir ve gider raporlarını görüntüleyin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-cash-register',
      priority: 7,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzun günlük, haftalık ve aylık gelir-gider raporlarını bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Günlük gelir toplamları',
            'Ödeme yöntemlerine göre dağılım',
            'Üyelik ödemeleri',
            'Ürün satış gelirleri',
            'Gider kayıtları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Kasa raporları finansal analiziniz için kritik öneme sahiptir. Düzenli olarak kontrol edin.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'expenses',
      title: 'Gider Yönetimi',
      description: 'Spor salonu giderlerinizi kaydedin ve takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-receipt',
      priority: 8,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuzun tüm giderlerini kategorilere ayırarak kaydedin ve raporlayın.'
        },
        {
          type: 'steps',
          content: [
            'Gider kategorisini seçin',
            'Gider tutarını girin',
            'Açıklama ekleyin',
            'Fatura/fiş bilgilerini kaydedin',
            'Gideri onaylayın'
          ]
        },
        {
          type: 'list',
          content: [
            'Kira giderleri',
            'Elektrik, su, doğalgaz',
            'Personel maaşları',
            'Ekipman bakım giderleri',
            'Temizlik malzemeleri'
          ]
        }
      ]
    });

    // Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'companyuserdetails',
      title: 'Salon Sahipleri',
      description: 'Sistem kullanıcılarını ve salon sahiplerini yönetin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-id-card',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sisteme kayıtlı salon sahiplerini ve yöneticilerini görüntüleyebilir, bilgilerini düzenleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Salon sahipleri listesi',
            'İletişim bilgileri',
            'Salon bağlantıları',
            'Hesap durumları',
            'Son giriş tarihleri'
          ]
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Detay bilgilerini inceleyin',
            'Gerektiğinde bilgileri güncelleyin',
            'Hesap durumlarını kontrol edin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'license-transactions',
      title: 'Satış Raporları',
      description: 'Lisans satış raporlarını görüntüleyin ve analiz edin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-receipt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistem genelindeki lisans satışlarını, gelir analizlerini ve ödeme raporlarını bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam satış tutarları',
            'Ödeme yöntemlerine göre dağılım',
            'Tarih aralığına göre filtreleme',
            'Admin bazlı satış raporları',
            'Paket bazlı analizler'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Raporları Excel formatında dışa aktararak detaylı analizler yapabilirsiniz.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    this.addGuide({
      id: 'deleted-companies',
      title: 'Pasif Salonlar',
      description: 'Silinen salonları görüntüleyin ve geri yükleyin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-trash-restore',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistemden silinen salonları görüntüleyebilir ve gerektiğinde geri yükleyebilirsiniz.'
        },
        {
          type: 'warning',
          content: 'Dikkat: Salon geri yükleme işlemi geri alınamaz. İşlem öncesi emin olun.',
          icon: 'fas fa-exclamation-triangle'
        },
        {
          type: 'steps',
          content: [
            'Silinen salonlar listesini görüntüleyin',
            'Salon detaylarını inceleyin',
            'Geri yükleme uygunluğunu kontrol edin',
            'Geri yükleme işlemini onaylayın'
          ]
        }
      ]
    });

    // Lisans Yönetimi Rehberleri
    this.addGuide({
      id: 'license-packages-add',
      title: 'Lisans Paketleri',
      description: 'Sistem lisans paketlerini oluşturun ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-box-open',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Farklı salon ihtiyaçları için lisans paketleri oluşturabilir ve mevcut paketleri yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Paket adını belirleyin',
            'Paket özelliklerini tanımlayın',
            'Fiyatlandırmayı ayarlayın',
            'Süre limitlerini belirleyin',
            'Paketi aktifleştirin'
          ]
        },
        {
          type: 'list',
          content: [
            'Temel paket (küçük salonlar)',
            'Standart paket (orta salonlar)',
            'Premium paket (büyük salonlar)',
            'Kurumsal paket (zincir salonlar)'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'user-licenses',
      title: 'Aktif Lisanslar',
      description: 'Sistemdeki aktif lisansları görüntüleyin ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-tag',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistemde aktif olan tüm lisansları görüntüleyebilir, süre uzatabilir veya iptal edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif lisans sayısı',
            'Lisans bitiş tarihleri',
            'Paket türleri',
            'Salon bilgileri',
            'Ödeme durumları'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Lisans iptal işlemleri salon operasyonlarını durdurur. Dikkatli olun.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'expired-licenses',
      title: 'Lisansı Dolan Üyeler',
      description: 'Süresi dolan lisansları görüntüleyin ve yenileyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-exclamation-triangle',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Lisans süresi dolan salonları görüntüleyebilir ve lisans yenileme işlemlerini gerçekleştirebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Süresi dolan lisansları listeleyin',
            'Salon sahipleriyle iletişime geçin',
            'Yenileme teklifini hazırlayın',
            'Ödeme alın ve lisansı yenileyin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Lisans bitiş tarihinden önce hatırlatma yaparak müşteri kaybını önleyin.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });

    // Sistem Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'devices',
      title: 'Aktif Cihazlar',
      description: 'Sisteme bağlı cihazları görüntüleyin ve yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-mobile-alt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sisteme bağlı tüm cihazları (mobil, tablet, bilgisayar) görüntüleyebilir ve güvenlik kontrolü yapabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif cihaz sayısı',
            'Cihaz türleri ve modelleri',
            'Son bağlantı tarihleri',
            'IP adresleri',
            'Kullanıcı bilgileri'
          ]
        },
        {
          type: 'warning',
          content: 'Dikkat: Şüpheli cihazları sistemden çıkararak güvenliği sağlayın.',
          icon: 'fas fa-exclamation-triangle'
        }
      ]
    });

    this.addGuide({
      id: 'cache-admin',
      title: 'Cache Yönetimi',
      description: 'Sistem önbelleğini yönetin ve performansı optimize edin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-database',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistem performansını artırmak için önbellek yönetimi yapabilir ve gerektiğinde temizleyebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Cache durumunu kontrol edin',
            'Bellek kullanımını görüntüleyin',
            'Gerektiğinde cache temizleyin',
            'Performans metriklerini inceleyin'
          ]
        },
        {
          type: 'tip',
          content: 'İpucu: Düzenli cache temizliği sistem performansını artırır ve hata riskini azaltır.',
          icon: 'fas fa-lightbulb'
        }
      ]
    });
  }

  private addGuide(guide: HelpGuide): void {
    this.guides.set(guide.id, guide);
  }

  getGuide(id: string): HelpGuide | undefined {
    return this.guides.get(id);
  }

  getGuidesByCategory(category: HelpCategory): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.category === category)
      .sort((a, b) => a.priority - b.priority);
  }

  getGuidesByRole(role: string): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.targetRoles.includes(role))
      .sort((a, b) => a.priority - b.priority);
  }

  openHelpDialog(guideId: string): Observable<any> {
    const guide = this.getGuide(guideId);
    if (!guide) {
      console.warn(`Help guide with id '${guideId}' not found`);
      return new Observable(observer => observer.complete());
    }

    const dialogRef = this.dialog.open(HelpDialogComponent, {
      width: '700px',
      maxWidth: '90vw',
      maxHeight: '85vh',
      data: { guide, showCloseButton: true } as HelpDialogData,
      disableClose: false,
      position: { top: '50px' },
      panelClass: 'help-dialog-container'
    });

    return dialogRef.afterClosed();
  }

  getAllGuides(): HelpGuide[] {
    return Array.from(this.guides.values())
      .sort((a, b) => a.priority - b.priority);
  }
}
