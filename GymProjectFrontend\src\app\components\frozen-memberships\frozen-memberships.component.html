<div class="container mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Dondurulmuş üyelikler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
    <!-- Toggle Button -->
    <div class="col-12 mb-3">
      <button class="modern-btn toggle-button" (click)="toggleView()">
        <i class="fas" [ngClass]="showFrozenMemberships ? 'fa-history' : 'fa-snowflake'"></i>
        {{ showFrozenMemberships ? 'Dondurma İşlem Geçmişi' : 'Dondurulmuş Üyelikler' }}
      </button>
    </div>
  
    <!-- Aktif <PERSON> Üyelikler -->
    <div class="col-12 mb-4" *ngIf="showFrozenMemberships">
      <div class="modern-card frozen-card slide-in-left">
        <div class="modern-card-header">
          <div class="d-flex align-items-center gap-2">
            <h5 class="mb-0">
              <i class="fas fa-snowflake me-2"></i>
              Dondurulmuş Üyelikler
            </h5>

            <!-- Help Button -->
            <app-help-button
              guideId="frozen-memberships"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>

          <!-- Özet Bilgiler -->
          <div class="frozen-summary">
            <div class="summary-item">
              <span class="summary-value">{{ frozenMemberships.length }}</span>
              <span class="summary-label">Toplam Dondurulmuş Üyelik</span>
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Üye Adı
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş
                  </th>
                  <th>
                    <i class="fas fa-calendar-plus me-2"></i>
                    Başlangıç
                  </th>
                  <th>
                    <i class="fas fa-calendar-minus me-2"></i>
                    Bitiş
                  </th>
                  <th>
                    <i class="fas fa-hourglass-half me-2"></i>
                    Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let membership of frozenMemberships" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar bg-info">
                        {{ membership.memberName.charAt(0) }}
                      </div>
                      <span>{{ membership.memberName }}</span>
                    </div>
                  </td>
                  <td>{{ membership.phoneNumber }}</td>
                  <td>
                    <span class="modern-badge modern-badge-info">{{ membership.branch }}</span>
                  </td>
                  <td>{{ membership.freezeStartDate | date:'dd/MM/yyyy' }}</td>
                  <td>{{ membership.freezeEndDate | date:'dd/MM/yyyy' }}</td>
                  <td>
                    <span class="remaining-days" [ngClass]="{'text-success': getRemainingDays(membership.freezeEndDate) > 10, 'text-warning': getRemainingDays(membership.freezeEndDate) <= 10 && getRemainingDays(membership.freezeEndDate) > 3, 'text-danger': getRemainingDays(membership.freezeEndDate) <= 3}">
                      {{ getRemainingDays(membership.freezeEndDate) }} gün
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="modern-btn modern-btn-danger modern-btn-sm" 
                              (click)="cancelFreeze(membership)"
                              [disabled]="isProcessing"
                              title="Dondurma işlemini tamamen iptal et">
                        <i class="fas fa-undo"></i>
                      </button>
                      <button class="modern-btn modern-btn-warning modern-btn-sm ms-2"
                              (click)="reactivateFromToday(membership)"
                              [disabled]="isProcessing"
                              title="Bugünden itibaren üyeliği aktif et">
                        <i class="fas fa-play"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="frozenMemberships.length === 0">
                  <td colspan="7" class="text-center py-4">
                    <i class="fas fa-snowflake fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">Dondurulmuş üyelik bulunmamaktadır.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  
    <!-- Geçmiş İşlemler -->
    <div class="col-12" *ngIf="!showFrozenMemberships">
      <div class="modern-card history-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-history me-2"></i>
            Dondurma İşlem Geçmişi
          </h5>
          
          <!-- Arama Kutusu -->
          <div class="search-container">
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                placeholder="İsimle ara..."
                [(ngModel)]="searchText"
                (input)="onSearchChange($event)"
              />
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Üye Adı
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş
                  </th>
                  <th>
                    <i class="fas fa-calendar-plus me-2"></i>
                    Başlangıç
                  </th>
                  <th>
                    <i class="fas fa-calendar-minus me-2"></i>
                    Bitiş
                  </th>
                  <th>
                    <i class="fas fa-calendar-check me-2"></i>
                    Gerçek Bitiş
                  </th>
                  <th>
                    <i class="fas fa-clock me-2"></i>
                    Süre
                  </th>
                  <th>
                    <i class="fas fa-hourglass-half me-2"></i>
                    Kullanılan
                  </th>
                  <th>
                    <i class="fas fa-tag me-2"></i>
                    İşlem Türü
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    İşlem Tarihi
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let history of filteredHistories" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar bg-secondary">
                        {{ history.memberName.charAt(0) }}
                      </div>
                      <span>{{ history.memberName }}</span>
                    </div>
                  </td>
                  <td>{{ history.phoneNumber }}</td>
                  <td>
                    <span class="modern-badge modern-badge-info">{{ history.branch }}</span>
                  </td>
                  <td>{{ formatDate(history.startDate) }}</td>
                  <td>{{ formatDate(history.plannedEndDate) }}</td>
                  <td>{{ history.actualEndDate ? formatDate(history.actualEndDate) : '-' }}</td>
                  <td>
                    <span class="modern-badge modern-badge-primary">{{ history.freezeDays }} gün</span>
                  </td>
                  <td>
                    <span class="modern-badge" [ngClass]="{'modern-badge-success': history.usedDays !== null, 'modern-badge-secondary': history.usedDays === null}">
                      {{ history.usedDays !== null ? history.usedDays + ' gün' : '-' }}
                    </span>
                  </td>
                  <td>
                    <span class="modern-badge" [ngClass]="{'modern-badge-danger': history.cancellationType === 'İptal', 'modern-badge-warning': history.cancellationType === 'Erken Aktifleştirme', 'modern-badge-secondary': !history.cancellationType}">
                      {{ history.cancellationType || '-' }}
                    </span>
                  </td>
                  <td>{{ formatDate(history.creationDate) }}</td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="filteredHistories.length === 0">
                  <td colspan="10" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">{{ searchText ? 'Arama sonucu bulunamadı.' : 'Dondurma geçmişi bulunmamaktadır.' }}</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    </div>

  </div>

  <!-- Geçmiş Detay Modal -->
  <div class="modern-modal" [class.show]="showHistoryModal">
    <div class="modern-modal-overlay" (click)="closeHistoryModal()"></div>
    <div class="modern-modal-container">
      <div class="modern-modal-header">
        <h5 class="modern-modal-title">
          <i class="fas fa-history me-2"></i>
          Üye Dondurma Geçmişi
        </h5>
        <button type="button" class="modern-modal-close" (click)="closeHistoryModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modern-modal-body">
        <div class="table-container">
          <table class="modern-table">
            <thead>
              <tr>
                <th>Başlangıç</th>
                <th>Bitiş</th>
                <th>Gerçek Bitiş</th>
                <th>Süre</th>
                <th>Kullanılan</th>
                <th>İşlem Türü</th>
                <th>İşlem Tarihi</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let history of getFilteredHistory()">
                <td>{{ formatDate(history.startDate) }}</td>
                <td>{{ formatDate(history.plannedEndDate) }}</td>
                <td>{{ history.actualEndDate ? formatDate(history.actualEndDate) : '-' }}</td>
                <td>{{ history.freezeDays }} gün</td>
                <td>{{ history.usedDays !== null ? history.usedDays + ' gün' : '-' }}</td>
                <td>{{ history.cancellationType || '-' }}</td>
                <td>{{ formatDate(history.creationDate) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modern-modal-footer">
        <button type="button" class="modern-btn modern-btn-secondary" (click)="closeHistoryModal()">
          <i class="fas fa-times me-2"></i>
          Kapat
        </button>
      </div>
    </div>
  </div>
