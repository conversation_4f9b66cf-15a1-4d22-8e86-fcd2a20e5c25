<div class="container mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Initial Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Ürün satış formu hazırlanıyor">
  </app-loading-spinner>

  <!-- Sale Processing Spinner -->
  <app-loading-spinner
    *ngIf="isSelling"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Satış işlemi gerçekleştiriliyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
      <!-- Sol Panel - Satış Formu -->
      <div class="col-lg-6 mb-4">
        <div class="modern-card slide-in-left h-100">
          <div class="modern-card-header">
            <div class="d-flex align-items-center gap-2">
              <h5 class="mb-0">
                <i class="fas fa-shopping-bag me-2"></i>
                Ür<PERSON>n <PERSON>ış Formu
              </h5>

              <!-- Help Button -->
              <app-help-button
                guideId="product-sale"
                position="inline"
                size="small"
                tooltip="Bu panel hakkında yardım al">
              </app-help-button>
            </div>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="saleForm" class="fade-in">
              <!-- Üye Bilgileri Section -->
              <div class="form-section mb-4">
                <h6 class="section-title">
                  <i class="fas fa-user me-2"></i>
                  Üye Bilgileri
                </h6>

                <div class="modern-form-group">
                  <label class="modern-form-label required">Üye Seçimi</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-user"></i>
                    </span>
                    <input type="text"
                           class="modern-form-control"
                           placeholder="Üye adı veya telefon numarası"
                           formControlName="member"
                           [matAutocomplete]="autoMember"
                           (blur)="validateMemberSelection()">
                    <mat-autocomplete #autoMember="matAutocomplete" [displayWith]="displayMember">
                      <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                        {{member.name}} - {{member.phoneNumber}}
                      </mat-option>
                    </mat-autocomplete>
                  </div>
                  <div *ngIf="memberSelectionInvalid && saleForm.get('member')?.touched" class="text-danger mt-1">
                    <small><i class="fas fa-exclamation-circle me-1"></i>Lütfen listeden bir üye seçiniz</small>
                  </div>
                </div>
              </div>

              <!-- Ürün Bilgileri Section -->
              <div class="form-section mb-4">
                <h6 class="section-title">
                  <i class="fas fa-box me-2"></i>
                  Ürün Bilgileri
                </h6>

                <div class="row">
                  <div class="col-md-8 mb-3">
                    <div class="modern-form-group">
                      <label class="modern-form-label required">Ürün Seçimi</label>
                      <div class="input-group">
                        <span class="input-group-text">
                          <i class="fas fa-box"></i>
                        </span>
                        <input type="text"
                               class="modern-form-control"
                               placeholder="Ürün adı"
                               formControlName="product"
                               [matAutocomplete]="autoProduct"
                               (blur)="validateProductSelection()">
                        <mat-autocomplete #autoProduct="matAutocomplete" [displayWith]="displayProduct">
                          <mat-option *ngFor="let product of filteredProducts | async" [value]="product">
                            {{product.name}} - {{product.price | currency:'TRY':'symbol-narrow':'1.2-2'}}
                          </mat-option>
                        </mat-autocomplete>
                      </div>
                      <div *ngIf="productSelectionInvalid && saleForm.get('product')?.touched" class="text-danger mt-1">
                        <small><i class="fas fa-exclamation-circle me-1"></i>Lütfen listeden bir ürün seçiniz</small>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-4 mb-3">
                    <div class="modern-form-group">
                      <label class="modern-form-label required">Miktar</label>
                      <div class="input-group">
                        <span class="input-group-text">
                          <i class="fas fa-hashtag"></i>
                        </span>
                        <input type="number"
                               class="modern-form-control"
                               placeholder="Miktar"
                               min="1"
                               formControlName="quantity">
                      </div>
                      <div *ngIf="saleForm.get('quantity')?.invalid && saleForm.get('quantity')?.touched" class="text-danger mt-1">
                        <small *ngIf="saleForm.get('quantity')?.errors?.['required']"><i class="fas fa-exclamation-circle me-1"></i>Miktar gerekli</small>
                        <small *ngIf="saleForm.get('quantity')?.errors?.['min']"><i class="fas fa-exclamation-circle me-1"></i>Miktar en az 1 olmalı</small>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Sepete Ekle Butonu -->
                <div class="mt-3">
                  <button class="modern-btn modern-btn-primary w-100 modern-btn-lg"
                          [disabled]="productSelectionInvalid || !saleForm.get('product')?.value || typeof saleForm.get('product')?.value === 'string' || saleForm.get('quantity')?.invalid"
                          (click)="addToCart()">
                    <i class="fas fa-cart-plus modern-btn-icon"></i>
                    Sepete Ekle
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Sağ Panel - Sepet -->
      <div class="col-lg-6">
        <div class="modern-card slide-in-right h-100">
          <div class="modern-card-header">
            <h5 class="mb-0">
              <i class="fas fa-shopping-cart me-2"></i>
              Sepet
            </h5>
            <span class="modern-badge modern-badge-primary" style="font-size: 1rem;">
              {{totalAmount | currency:'TRY':'symbol-narrow':'1.2-2'}}
            </span>
          </div>

          <div class="modern-card-body d-flex flex-column">
            <!-- Boş Sepet -->
            <div *ngIf="cartItems.length === 0" class="empty-cart-container flex-grow-1 d-flex flex-column justify-content-center align-items-center">
              <div class="empty-cart-icon mb-3">
                <i class="fas fa-shopping-cart fa-4x text-primary"></i>
              </div>
              <h6 class="text-primary mb-2">Sepetiniz Boş</h6>
              <p class="text-muted text-center">Ürün eklemek için soldaki formu kullanın</p>
            </div>

            <!-- Sepet İçeriği -->
            <div *ngIf="cartItems.length > 0" class="d-flex flex-column h-100">
              <!-- Sepet Öğeleri -->
              <div class="cart-items flex-grow-1 mb-4">
                <div *ngFor="let item of cartItems; let i = index"
                     class="cart-item mb-3 zoom-in">
                  <div class="modern-card-item">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="flex-grow-1">
                        <h6 class="mb-2 text-primary">{{getProductName(item.productId)}}</h6>
                        <div class="d-flex align-items-center gap-2 mb-2">
                          <span class="modern-badge modern-badge-info">
                            <i class="fas fa-cubes me-1"></i>
                            {{item.quantity}} adet
                          </span>
                          <span class="modern-badge modern-badge-secondary">
                            <i class="fas fa-lira-sign me-1"></i>
                            {{item.unitPrice | currency:'TRY':'symbol-narrow':'1.2-2'}}
                          </span>
                        </div>
                        <div class="item-total">
                          <strong class="text-success">
                            <i class="fas fa-equals me-1"></i>
                            {{item.quantity * item.unitPrice | currency:'TRY':'symbol-narrow':'1.2-2'}}
                          </strong>
                        </div>
                      </div>
                      <button class="modern-btn modern-btn-danger modern-btn-sm ms-3"
                              (click)="removeFromCart(i)"
                              title="Sepetten Çıkar">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Sepet Özeti ve Satış Butonu -->
              <div class="cart-footer">
                <div class="cart-summary mb-3 p-3" style="background-color: var(--bg-secondary); border-radius: var(--border-radius-md);">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">Toplam:</span>
                    <span class="fw-bold text-primary fs-5">
                      {{totalAmount | currency:'TRY':'symbol-narrow':'1.2-2'}}
                    </span>
                  </div>
                </div>

                <button class="modern-btn modern-btn-success w-100 modern-btn-lg"
                        [disabled]="memberSelectionInvalid || !saleForm.get('member')?.value || typeof saleForm.get('member')?.value === 'string' || cartItems.length === 0 || isSelling"
                        (click)="sell()">
                  <i class="fas fa-check-circle modern-btn-icon" *ngIf="!isSelling"></i>
                  <i class="fas fa-spinner fa-spin modern-btn-icon" *ngIf="isSelling"></i>
                  {{ isSelling ? 'İşleniyor...' : 'Satışı Tamamla' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
