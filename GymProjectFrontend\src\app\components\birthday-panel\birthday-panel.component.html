<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}" *ngIf="!isDialog">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Doğum günleri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

  <!-- Page Header with Help Button -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex align-items-center gap-2">
            <h5 class="mb-0">
              <i class="fas fa-birthday-cake me-2"></i>
              Doğum Günleri
            </h5>

            <!-- Help Button -->
            <app-help-button
              guideId="birthdays"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>
          <p class="text-muted mb-0 mt-1">
            Yaklaşan doğum günlerinin takibi ve kutlama yönetimi
          </p>
        </div>
      </div>
    </div>
  </div>

    <div class="row">
      <div class="col-md-12">
        <!-- Main Card -->
        <div class="card">
          <div class="card-header">
            <div class="d-flex align-items-center gap-2">
              <h5 class="mb-0">
                <i class="fas fa-birthday-cake me-2"></i>
                Yaklaşan Doğum Günleri
              </h5>
            </div>
          </div>
          <div class="modern-card-body">
          <!-- Empty State -->
          <div *ngIf="members.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-birthday-cake fa-4x"></i>
            </div>
            <h4>Yaklaşan Doğum Günü Yok</h4>
            <p>Önümüzdeki 3 gün içinde doğum günü olan üye bulunmamaktadır.</p>
          </div>

          <!-- Members Table -->
          <div *ngIf="members.length > 0" class="table-responsive">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Üye Bilgileri
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    İletişim
                  </th>
                  <th>
                    <i class="fas fa-calendar-alt me-2"></i>
                    Doğum Tarihi
                  </th>
                  <th>
                    <i class="fas fa-hourglass-half me-2"></i>
                    Kalan Gün
                  </th>
                  <th class="text-center">
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <td>
                    <div class="member-info">
                      <div class="modern-avatar" [style.background-color]="getAvatarColor(member.name)">
                        {{ getInitials(member.name) }}
                      </div>
                      <div class="member-details">
                        <div class="member-name">{{ member.name }}</div>
                        <div class="member-id">ID: {{ member.memberID }}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="contact-info">
                      <div class="phone-number">
                        <i class="fas fa-phone me-1"></i>
                        {{ member.phoneNumber }}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="birth-date">
                      <i class="fas fa-calendar me-1"></i>
                      {{ getFormattedBirthDate(member.birthDate) }}
                    </div>
                  </td>
                  <td>
                    <span [ngClass]="getDaysLeftClass(getDaysUntilBirthday(member.birthDate))">
                      <i class="fas fa-clock me-1"></i>
                      {{ getDaysUntilBirthday(member.birthDate) }} gün
                    </span>
                  </td>
                  <td class="text-center">
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-outline-success modern-btn-sm"
                        (click)="openWhatsApp(member.phoneNumber)"
                        title="WhatsApp ile Doğum Günü Mesajı Gönder"
                      >
                        <i class="fab fa-whatsapp me-1"></i>
                        Mesaj Gönder
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    </div>

  </div>
</div>

<!-- Dialog version -->
<div class="birthday-panel-container" *ngIf="isDialog">
  <div class="dialog-header">
    <h2>
      <i class="fas fa-birthday-cake me-2"></i>
      Yaklaşan Doğum Günleri
    </h2>
  </div>

  <div class="dialog-content">
    <!-- Empty State -->
    <div *ngIf="members.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-birthday-cake fa-4x"></i>
      </div>
      <h4>Yaklaşan Doğum Günü Yok</h4>
      <p>Önümüzdeki 3 gün içinde doğum günü olan üye bulunmamaktadır.</p>
    </div>

    <!-- Members List -->
    <div *ngIf="members.length > 0" class="dialog-members-list">
      <div class="dialog-member-card" *ngFor="let member of members">
        <div class="member-info">
          <div class="modern-avatar" [style.background-color]="getAvatarColor(member.name)">
            {{ getInitials(member.name) }}
          </div>
          <div class="member-details">
            <div class="member-name">{{ member.name }}</div>
            <div class="member-birth-info">
              <span class="birth-date">
                <i class="fas fa-calendar me-1"></i>
                {{ getFormattedBirthDate(member.birthDate) }}
              </span>
              <span [ngClass]="getDaysLeftClass(getDaysUntilBirthday(member.birthDate))">
                <i class="fas fa-clock me-1"></i>
                {{ getDaysUntilBirthday(member.birthDate) }} gün kaldı
              </span>
            </div>
          </div>
        </div>
        <div class="member-actions">
          <button
            class="modern-btn modern-btn-outline-success modern-btn-sm"
            (click)="openWhatsApp(member.phoneNumber)"
            title="WhatsApp ile Doğum Günü Mesajı Gönder"
          >
            <i class="fab fa-whatsapp me-1"></i>
            Mesaj Gönder
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="dialog-actions">
    <button class="modern-btn modern-btn-secondary" (click)="close()">
      <i class="fas fa-times me-1"></i>
      Kapat
    </button>
  </div>
</div>
