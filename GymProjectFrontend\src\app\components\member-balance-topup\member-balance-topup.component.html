<div class="container mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Bakiye bilgileri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">
    <div class="row">
      <!-- Bakiye İstatistikleri -->
      <div class="col-md-12 mb-4">
        <div class="modern-card">
          <div class="modern-card-header">
            <div class="d-flex align-items-center gap-2">
              <h5>Bakiye İstatistikleri</h5>

              <!-- Help Button -->
              <app-help-button
                guideId="memberbalancetopup"
                position="inline"
                size="small"
                tooltip="Bu panel hakkında yardım al">
              </app-help-button>
            </div>
            <div>

            </div>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-4 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--primary-light);">
                  <div class="modern-stats-icon" style="background-color: var(--primary);">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getTotalMembersWithBalance()}}</div>
                    <div class="modern-stats-label">Bakiyeli Üye</div>
                  </div>
                </div>
              </div>
              <div class="col-md-4 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--success-light);">
                  <div class="modern-stats-icon" style="background-color: var(--success);">
                    <i class="fas fa-wallet"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getTotalPositiveBalance() | currency:'TRY':'symbol-narrow':'1.2-2'}}</div>
                    <div class="modern-stats-label">Toplam Pozitif Bakiye</div>
                  </div>
                </div>
              </div>
              <div class="col-md-4 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--danger-light);">
                  <div class="modern-stats-icon" style="background-color: var(--danger);">
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getTotalNegativeBalance() | currency:'TRY':'symbol-narrow':'1.2-2'}}</div>
                    <div class="modern-stats-label">Toplam Negatif Bakiye</div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      <!-- Bakiye Yükleme Formu -->
      <div class="col-md-4">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5>Bakiye Yükleme</h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="topupForm" (ngSubmit)="topup()">
              <div class="modern-form-group">
                <label for="memberSelect" class="modern-form-label">Üye Seçin</label>
                <div class="input-group">
                  <div class="input-group-text">
                    <i class="fas fa-user"></i>
                  </div>
                  <input id="memberSelect" type="text" class="modern-form-control" [formControl]="memberControl" [matAutocomplete]="auto" placeholder="Üye adı veya telefon" style="width: 70%;">
                </div>
                <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayMember">
                  <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                    {{member.name}} - {{member.phoneNumber}}
                  </mat-option>
                </mat-autocomplete>
              </div>
              <div class="modern-form-group">
                <label for="amount" class="modern-form-label">Yüklenecek TL</label>
                <div class="input-group">
                  <div class="input-group-text">
                    <i class="fas fa-money-bill"></i>
                  </div>
                  <input type="number" class="modern-form-control" id="amount" formControlName="amount" placeholder="0.00" style="width: 70%;">
                </div>
              </div>
              <button type="submit" class="modern-btn modern-btn-primary w-100" [disabled]="!topupForm.valid || !memberControl.value">
                <i class="fas fa-plus-circle modern-btn-icon"></i> Bakiye Yükle
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Bakiye Listesi -->
      <div class="col-md-8">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <h5>Bakiye Listesi</h5>
            <div class="d-flex align-items-center">
              <div class="position-relative me-2">
                <input type="text" class="modern-form-control" placeholder="Ara..." [(ngModel)]="searchTerm" (input)="filterMembers()">
                <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
              </div>
              <div class="dropdown">
                <button class="modern-filter-btn dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fas fa-filter me-2"></i>
                  <span class="filter-text">{{getFilterText()}}</span>
                  <span class="filter-badge">{{paginatedMembers.totalCount}}</span>
                </button>
                <ul class="dropdown-menu modern-dropdown-menu" aria-labelledby="filterDropdown">
                  <li>
                    <a class="dropdown-item modern-dropdown-item"
                       [class.active]="balanceFilter === 'all'"
                       (click)="filterByBalanceType('all')">
                      <i class="fas fa-list me-2"></i>
                      <span>Tümü</span>
                      <span class="item-count">{{getTotalMembersWithBalance()}}</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-dropdown-item"
                       [class.active]="balanceFilter === 'positive'"
                       (click)="filterByBalanceType('positive')">
                      <i class="fas fa-arrow-up me-2 text-success"></i>
                      <span>Pozitif Bakiye</span>
                      <span class="item-count">{{getPositiveBalanceCount()}}</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-dropdown-item"
                       [class.active]="balanceFilter === 'negative'"
                       (click)="filterByBalanceType('negative')">
                      <i class="fas fa-arrow-down me-2 text-danger"></i>
                      <span>Negatif Bakiye</span>
                      <span class="item-count">{{getNegativeBalanceCount()}}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      Adı <i class="fas" [ngClass]="getSortIcon('name')"></i>
                    </th>
                    <th>
                      Telefon <i class="fas" [ngClass]="getSortIcon('phoneNumber')"></i>
                    </th>
                    <th>
                      Bakiye <i class="fas" [ngClass]="getSortIcon('balance')"></i>
                    </th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let member of paginatedMembers.data">
                    <td>{{member.name}}</td>
                    <td>{{member.phoneNumber}}</td>
                    <td>
                      <span [ngClass]="{'text-success': member.balance > 0, 'text-danger': member.balance < 0}">
                        {{member.balance | currency:'TRY':'symbol-narrow':'1.2-2'}}
                      </span>
                    </td>
                    <td>
                      <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openUpdateDialog(member)">
                        <i class="fas fa-edit"></i> Güncelle
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="paginatedMembers.data.length === 0">
                    <td colspan="4" class="text-center py-3">Bakiyeli üye bulunamadı</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
              <div class="d-flex align-items-center mb-2 mb-md-0">
                <span class="modern-badge modern-badge-primary me-3">
                  Toplam: {{paginatedMembers.totalCount}} üye
                </span>
                <div class="d-flex align-items-center">
                  <label class="form-label me-2 mb-0">Sayfa başına:</label>
                  <select class="form-select form-select-sm" style="width: auto;"
                          [(ngModel)]="pageSize" (change)="changePageSize(pageSize)">
                    <option *ngFor="let size of pageSizeOptions" [value]="size">{{size}}</option>
                  </select>
                </div>
              </div>

              <nav *ngIf="paginatedMembers.totalPages > 1">
                <ul class="modern-pagination">
                  <li class="modern-page-item" [class.disabled]="!paginatedMembers.hasPrevious">
                    <a class="modern-page-link" (click)="goToPage(currentPage - 1)" aria-label="Previous">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                  <li class="modern-page-item" *ngFor="let page of getPaginationRange()"
                      [class.active]="page === currentPage">
                    <a class="modern-page-link" (click)="goToPage(page)">{{page}}</a>
                  </li>
                  <li class="modern-page-item" [class.disabled]="!paginatedMembers.hasNext">
                    <a class="modern-page-link" (click)="goToPage(currentPage + 1)" aria-label="Next">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
