<div class="container-fluid p-0 deleted-companies-container">
  <!-- Modern Header Section -->
  <div class="deleted-companies-header">
    <div class="header-content">
      <div class="header-left">
        <div class="header-icon">
          <i class="fas fa-trash-restore"></i>
        </div>
        <div class="header-text">
          <div class="d-flex align-items-center gap-2">
            <h1 class="header-title">Pasif <PERSON></h1>

            <!-- Help Button -->
            <app-help-button
              guideId="deleted-companies"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>
          <p class="header-subtitle">Silinen salon sahipleri ve salonları yönetin</p>
        </div>
      </div>
      <div class="header-actions">
        <div class="stats-card" *ngIf="deletedCompanies.length > 0">
          <div class="stats-number">{{ deletedCompanies.length }}</div>
          <div class="stats-label"><PERSON><PERSON><PERSON></div>
        </div>
        <div class="action-buttons">
          <button
            class="btn-action btn-action-primary"
            (click)="toggleViewMode()"
            [title]="viewMode === 'table' ? 'Kart Görünümü' : 'Tablo Görünümü'"
          >
            <i [class]="viewMode === 'table' ? 'fas fa-th-large' : 'fas fa-table'"></i>
            <span>{{ viewMode === 'table' ? 'Kart' : 'Tablo' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="deleted-companies-content">
    <div class="content-wrapper">

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <h3 class="loading-title">Pasif salonlar yükleniyor</h3>
          <p class="loading-subtitle">Lütfen bekleyin</p>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && deletedCompanies.length === 0" class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <i class="fas fa-trash-restore"></i>
          </div>
          <h3 class="empty-title">Silinen salon bulunamadı</h3>
          <p class="empty-subtitle">Henüz silinmiş salon bulunmamaktadır.</p>
        </div>
      </div>

      <!-- Table View -->
      <div *ngIf="!isLoading && deletedCompanies.length > 0 && viewMode === 'table'" class="table-container">
        <div class="table-wrapper">
          <table class="deleted-companies-table">
            <thead>
              <tr>
                <th class="th-user">Salon Sahibi</th>
                <th class="th-contact">İletişim</th>
                <th class="th-company">Salon Bilgileri</th>
                <th class="th-stats">İstatistikler</th>
                <th class="th-date">Silme Tarihi</th>
                <th class="th-actions">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let company of deletedCompanies; let i = index"
                  class="table-row"
                  [style.animation-delay]="(i * 0.1) + 's'">
                <td class="td-user">
                  <div class="user-info">
                    <div class="user-avatar" [style.background-color]="getAvatarColor(company.name)">
                      {{ getInitials(company.name) }}
                    </div>
                    <div class="user-details">
                      <div class="user-name">{{ company.name }}</div>
                      <div class="user-location">{{ company.cityName }}, {{ company.townName }}</div>
                    </div>
                  </div>
                </td>
                <td class="td-contact">
                  <div class="contact-info">
                    <div class="contact-item">
                      <i class="fas fa-envelope"></i>
                      <span>{{ company.email }}</span>
                    </div>
                    <div class="contact-item">
                      <i class="fas fa-phone"></i>
                      <span>{{ company.phoneNumber }}</span>
                    </div>
                  </div>
                </td>
                <td class="td-company">
                  <div class="company-info" *ngIf="company.companyName; else noCompanyInfo">
                    <div class="company-name">{{ company.companyName }}</div>
                    <div class="company-phone" *ngIf="company.companyPhone">
                      <i class="fas fa-building"></i>
                      {{ company.companyPhone }}
                    </div>
                  </div>
                  <ng-template #noCompanyInfo>
                    <div class="no-data">
                      <i class="fas fa-minus-circle"></i>
                      <span>Salon bilgisi yok</span>
                    </div>
                  </ng-template>
                </td>
                <td class="td-stats">
                  <div class="stats-info">
                    <div class="stat-item">
                      <div class="stat-number">{{ company.totalMembers }}</div>
                      <div class="stat-label">Üye</div>
                    </div>
                  </div>
                </td>
                <td class="td-date">
                  <div class="date-info">
                    <div class="date-value">{{ formatDate(company.deletedDate) }}</div>
                    <div class="date-label">Silindi</div>
                  </div>
                </td>
                <td class="td-actions">
                  <div class="action-buttons-table">
                    <button
                      class="btn-restore"
                      title="Geri Yükle"
                      (click)="restoreCompany(company)"
                      [disabled]="!company.canRestore"
                    >
                      <i class="fas fa-undo"></i>
                      <span>Geri Yükle</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Card View -->
      <div *ngIf="!isLoading && deletedCompanies.length > 0 && viewMode === 'card'" class="cards-container">
        <div class="cards-grid">
          <div class="company-card"
               *ngFor="let company of deletedCompanies; let i = index"
               [style.animation-delay]="(i * 0.1) + 's'">
            <div class="card-header-section">
              <div class="company-avatar" [style.background-color]="getAvatarColor(company.name)">
                {{ getInitials(company.name) }}
              </div>
              <div class="card-status">
                <div class="status-indicator deleted"></div>
                <span class="status-text">Silindi</span>
              </div>
            </div>

            <div class="card-content">
              <h3 class="company-name">{{ company.name }}</h3>
              <div class="company-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ company.cityName }}, {{ company.townName }}</span>
              </div>

              <div class="contact-section">
                <div class="contact-row">
                  <i class="fas fa-envelope"></i>
                  <span>{{ company.email }}</span>
                </div>
                <div class="contact-row">
                  <i class="fas fa-phone"></i>
                  <span>{{ company.phoneNumber }}</span>
                </div>
              </div>

              <div class="company-section" *ngIf="company.companyName">
                <div class="section-title">Salon Bilgileri</div>
                <div class="company-detail">
                  <i class="fas fa-building"></i>
                  <span>{{ company.companyName }}</span>
                </div>
                <div class="company-detail" *ngIf="company.companyPhone">
                  <i class="fas fa-phone-alt"></i>
                  <span>{{ company.companyPhone }}</span>
                </div>
              </div>

              <div class="stats-section">
                <div class="stat-card">
                  <div class="stat-number">{{ company.totalMembers }}</div>
                  <div class="stat-label">Üye Sayısı</div>
                </div>
                <div class="stat-card">
                  <div class="stat-date">{{ formatDate(company.deletedDate) }}</div>
                  <div class="stat-label">Silme Tarihi</div>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <button
                class="btn-restore-card"
                (click)="restoreCompany(company)"
                [disabled]="!company.canRestore"
              >
                <i class="fas fa-undo"></i>
                <span>Geri Yükle</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
